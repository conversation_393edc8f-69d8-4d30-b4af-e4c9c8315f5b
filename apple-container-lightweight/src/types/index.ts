// 容器相关类型定义
export interface Container {
  id: string
  name: string
  image: string
  status: 'running' | 'stopped' | 'paused' | 'restarting' | 'removing' | 'dead' | 'created' | 'exited'
  state: string
  ports: Port[]
  created: string
  started?: string
  finished?: string
  labels: Record<string, string>
  mounts: Mount[]
  networks: Record<string, NetworkInfo>
  stats?: ContainerStats
}

export interface Port {
  privatePort: number
  publicPort?: number
  type: 'tcp' | 'udp'
  ip?: string
}

export interface Mount {
  type: 'bind' | 'volume' | 'tmpfs'
  source: string
  destination: string
  mode: string
  rw: boolean
}

export interface NetworkInfo {
  networkId: string
  endpointId: string
  gateway: string
  ipAddress: string
  ipPrefixLen: number
  ipv6Gateway: string
  globalIPv6Address: string
  globalIPv6PrefixLen: number
  macAddress: string
}

export interface ContainerStats {
  cpuPercent: number
  memoryUsage: number
  memoryLimit: number
  memoryPercent: number
  networkRx: number
  networkTx: number
  blockRead: number
  blockWrite: number
  pids: number
}

// 镜像相关类型定义
export interface Image {
  id: string
  parentId: string
  repoTags: string[]
  repoDigests: string[]
  created: string
  size: number
  virtualSize: number
  sharedSize: number
  labels: Record<string, string>
  containers: number
}

// 系统信息类型定义
export interface SystemInfo {
  id: string
  containers: number
  containersRunning: number
  containersPaused: number
  containersStopped: number
  images: number
  driver: string
  driverStatus: Array<[string, string]>
  systemStatus: Array<[string, string]>
  plugins: {
    volume: string[]
    network: string[]
    authorization: string[]
    log: string[]
  }
  memoryLimit: boolean
  swapLimit: boolean
  kernelMemory: boolean
  cpuCfsPeriod: boolean
  cpuCfsQuota: boolean
  cpuShares: boolean
  cpuSet: boolean
  pidsLimit: boolean
  ipv4Forwarding: boolean
  bridgeNfIptables: boolean
  bridgeNfIp6tables: boolean
  debug: boolean
  nfd: number
  oomKillDisable: boolean
  ngoroutines: number
  systemTime: string
  loggingDriver: string
  cgroupDriver: string
  nEventsListener: number
  kernelVersion: string
  operatingSystem: string
  osType: string
  architecture: string
  indexServerAddress: string
  registryConfig: {
    allowNondistributableArtifactsCIDRs: string[]
    allowNondistributableArtifactsHostnames: string[]
    insecureRegistryCIDRs: string[]
    indexConfigs: Record<string, any>
    mirrors: string[]
  }
  ncpu: number
  memTotal: number
  genericResources: any[]
  dockerRootDir: string
  httpProxy: string
  httpsProxy: string
  noProxy: string
  name: string
  labels: string[]
  experimentalBuild: boolean
  serverVersion: string
  clusterStore: string
  clusterAdvertise: string
  runtimes: Record<string, any>
  defaultRuntime: string
  swarm: {
    nodeId: string
    nodeAddr: string
    localNodeState: string
    controlAvailable: boolean
    error: string
    remoteManagers: Array<{
      nodeId: string
      addr: string
    }>
  }
  liveRestoreEnabled: boolean
  isolation: string
  initBinary: string
  containerdCommit: {
    id: string
    expected: string
  }
  runcCommit: {
    id: string
    expected: string
  }
  initCommit: {
    id: string
    expected: string
  }
  securityOptions: string[]
}

// API响应类型定义
export interface ApiResponse<T = any> {
  success: boolean
  data?: T
  message?: string
  error?: string
  timestamp: string
}

// 应用状态类型定义
export interface AppState {
  theme: 'light' | 'dark' | 'auto'
  sidebarCollapsed: boolean
  loading: boolean
  error: string | null
  notifications: Notification[]
}

export interface Notification {
  id: string
  type: 'success' | 'error' | 'warning' | 'info'
  title: string
  message: string
  timestamp: string
  read: boolean
}

// 路由类型定义
export interface RouteConfig {
  path: string
  name: string
  component: any
  meta?: {
    title?: string
    icon?: string
    requiresAuth?: boolean
    roles?: string[]
  }
  children?: RouteConfig[]
}

// WebSocket消息类型定义
export interface WebSocketMessage {
  type: 'container_update' | 'image_update' | 'system_update' | 'stats_update' | 'log_update'
  data: any
  timestamp: string
}

// 用户偏好设置类型定义
export interface UserPreferences {
  theme: 'light' | 'dark' | 'auto'
  language: 'zh-CN' | 'en-US'
  autoRefresh: boolean
  refreshInterval: number
  showSystemContainers: boolean
  defaultView: 'grid' | 'list'
  notifications: {
    containerEvents: boolean
    systemAlerts: boolean
    performanceWarnings: boolean
  }
}

// 图表数据类型定义
export interface ChartData {
  labels: string[]
  datasets: Array<{
    label: string
    data: number[]
    backgroundColor?: string | string[]
    borderColor?: string | string[]
    borderWidth?: number
    fill?: boolean
  }>
}

export interface MetricData {
  timestamp: string
  value: number
  label?: string
}

// 操作日志类型定义
export interface OperationLog {
  id: string
  action: string
  target: string
  targetId: string
  result: 'success' | 'error'
  message: string
  timestamp: string
  userId?: string
  duration?: number
}
