<template>
  <div id="app" class="apple-container-studio">
    <!-- macOS风格标题栏 -->
    <div class="title-bar">
      <div class="traffic-lights">
        <div class="traffic-light close"></div>
        <div class="traffic-light minimize"></div>
        <div class="traffic-light maximize"></div>
      </div>
      <div class="title-bar-title">
        <AppleIcon name="apple" size="medium" variant="fill" />
        Container Studio
      </div>
      <div class="title-bar-controls">
        <button class="control-button" @click="toggleTheme" title="切换主题">
          <AppleIcon :name="isDark ? 'sun' : 'moon'" size="small" />
        </button>
        <button class="control-button" @click="refreshAll" title="刷新">
          <AppleIcon name="refresh" size="small" />
        </button>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="main-container">
      <!-- 侧边栏导航 -->
      <nav class="sidebar">
        <!-- 智能中心标题区域 -->
        <div class="sidebar-header">
          <router-link to="/intelligence-center" class="intelligence-title">
            <span class="intelligence-icon">
              <AppleIcon name="intelligence" size="large" variant="fill" />
            </span>
            <div class="intelligence-text">
              <h2 class="title-main">智能中心</h2>
              <span class="title-sub">AI资源中心</span>
            </div>
            <div class="ai-badge">AI</div>
          </router-link>
        </div>

        <div class="nav-menu">
          <router-link
            v-for="item in filteredNavigationMenus"
            :key="item.name"
            :to="item.path"
            class="nav-item"
            :class="{
              active: route.path === item.path,
              special: item.special,
              featured: item.featured
            }"
          >
            <span class="nav-icon">
              <AppleIcon
                :name="item.icon"
                size="large"
                :active="route.path === item.path"
                :variant="route.path === item.path ? 'fill' : 'regular'"
              />
            </span>
            <span class="nav-text">{{ item.title }}</span>
            <span class="nav-description">{{ item.description }}</span>

            <!-- 特殊标识 -->
            <div v-if="item.featured" class="featured-badge">
              <span class="badge-text">AI</span>
            </div>
          </router-link>
        </div>

        <div class="sidebar-footer">
          <div class="system-info">
            <!-- 硬件信息行：APPLE M4 Pro  24GB   ⚡16-Core Neural Engine -->
            <div class="info-row hardware-row">
              <div class="left-info">
                <span class="hardware-model">APPLE M4 Pro</span>
                <span class="memory-info">24GB</span>
              </div>
              <div class="right-info">
                <span class="neural-badge">
                  <span class="neural-icon">⚡</span>
                  16-Core Neural Engine
                </span>
              </div>
            </div>

            <!-- 系统信息行：MacOS Tahoe 26       Apple Intelligence -->
            <div class="info-row system-row">
              <div class="left-info">
                <span class="system-name">MacOS Tahoe 26</span>
              </div>
              <div class="right-info">
                <span class="ai-badge">
                  <span class="ai-icon">🧠</span>
                  Apple Intelligence
                </span>
              </div>
            </div>

            <!-- 时间信息行：                     2025.7.25  周五 -->
            <div class="info-row time-row">
              <div class="left-info"></div>
              <div class="right-info">
                <span class="date-info">{{ currentDate }}</span>
              </div>
            </div>
          </div>
        </div>
      </nav>

      <!-- 主内容区域 -->
      <main class="content-area">
        <router-view />
      </main>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { useRoute } from 'vue-router'
import { navigationMenus } from './router'
import AppleIcon from './components/AppleIcon.vue'

const route = useRoute()

const isDark = ref(false)

// 过滤掉智能中心项目，避免重复显示
const filteredNavigationMenus = computed(() => {
  return navigationMenus.filter(item => item.name !== 'IntelligenceCenter')
})

// 当前日期和星期
const currentDate = computed(() => {
  const now = new Date()
  const year = now.getFullYear()
  const month = (now.getMonth() + 1).toString().padStart(2, '0')
  const day = now.getDate().toString().padStart(2, '0')
  const weekdays = ['周日', '周一', '周二', '周三', '周四', '周五', '周六']
  const weekday = weekdays[now.getDay()]
  return `${year}.${month}.${day}  ${weekday}`
})

const toggleTheme = () => {
  isDark.value = !isDark.value
  document.documentElement.classList.toggle('dark', isDark.value)
}

const refreshAll = () => {
  window.location.reload()
}

onMounted(() => {
  // 检测系统主题偏好
  const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches
  isDark.value = prefersDark
  document.documentElement.classList.toggle('dark', prefersDark)
})
</script>

<style>
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

:root {
  /* 浅色主题 */
  --bg-primary: #ffffff;
  --bg-secondary: #f5f5f7;
  --bg-tertiary: #fbfbfd;
  --text-primary: #1d1d1f;
  --text-secondary: #86868b;
  --text-tertiary: #6e6e73;
  --border-color: #d2d2d7;
  --accent-blue: #007aff;
  --accent-green: #34c759;
  --accent-red: #ff3b30;
  --accent-orange: #ff9500;
  --shadow-light: rgba(0, 0, 0, 0.1);
  --shadow-medium: rgba(0, 0, 0, 0.15);
  --glass-bg: rgba(255, 255, 255, 0.8);
  --glass-border: rgba(255, 255, 255, 0.3);

  /* 苹果风格渐变背景 */
  --gradient-primary: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  --gradient-secondary: linear-gradient(135deg, #ffffff 0%, #f5f5f7 100%);
  --gradient-card: linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(255, 255, 255, 0.7) 100%);
  --gradient-sidebar: linear-gradient(180deg, rgba(255, 255, 255, 0.95) 0%, rgba(245, 245, 247, 0.9) 100%);
  --gradient-header: linear-gradient(90deg, rgba(255, 255, 255, 0.9) 0%, rgba(251, 251, 253, 0.8) 100%);

  /* 图标尺寸 */
  --icon-small: 16px;
  --icon-medium: 20px;
  --icon-large: 24px;
  --icon-xlarge: 32px;
  --icon-title: 36px;
}

:root.dark {
  /* 深色主题 */
  --bg-primary: #1c1c1e;
  --bg-secondary: #2c2c2e;
  --bg-tertiary: #3a3a3c;
  --text-primary: #ffffff;
  --text-secondary: #98989d;
  --text-tertiary: #8e8e93;
  --border-color: #38383a;
  --accent-blue: #0a84ff;
  --accent-green: #30d158;
  --accent-red: #ff453a;
  --accent-orange: #ff9f0a;
  --shadow-light: rgba(0, 0, 0, 0.3);
  --shadow-medium: rgba(0, 0, 0, 0.5);
  --glass-bg: rgba(28, 28, 30, 0.8);
  --glass-border: rgba(255, 255, 255, 0.1);

  /* 深色主题渐变背景 */
  --gradient-primary: linear-gradient(135deg, #1c1c1e 0%, #2c2c2e 100%);
  --gradient-secondary: linear-gradient(135deg, #2c2c2e 0%, #3a3a3c 100%);
  --gradient-card: linear-gradient(135deg, rgba(44, 44, 46, 0.9) 0%, rgba(58, 58, 60, 0.7) 100%);
  --gradient-sidebar: linear-gradient(180deg, rgba(28, 28, 30, 0.95) 0%, rgba(44, 44, 46, 0.9) 100%);
  --gradient-header: linear-gradient(90deg, rgba(28, 28, 30, 0.9) 0%, rgba(44, 44, 46, 0.8) 100%);
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Display', 'SF Pro Text', 'Helvetica Neue', Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background: var(--gradient-primary);
  color: var(--text-primary);
  overflow: hidden;
  position: relative;
}

body::before {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: var(--gradient-primary);
  z-index: -1;
}

.apple-container-studio {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: transparent;
  position: relative;
}

/* macOS风格标题栏 */
.title-bar {
  height: 52px;
  background: var(--gradient-header);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border-bottom: 1px solid var(--border-color);
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 20px;
  position: relative;
  z-index: 1000;
  box-shadow: 0 1px 3px var(--shadow-light);
}

.traffic-lights {
  display: flex;
  gap: 8px;
  align-items: center;
}

.traffic-light {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  cursor: pointer;
  transition: all 0.2s ease;
}

.traffic-light.close {
  background: #ff5f57;
}

.traffic-light.minimize {
  background: #ffbd2e;
}

.traffic-light.maximize {
  background: #28ca42;
}

.traffic-light:hover {
  transform: scale(1.1);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

.title-bar-title {
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  font-weight: 600;
  color: var(--text-primary);
}

.app-icon {
  font-size: 16px;
}

.title-bar-controls {
  display: flex;
  gap: 8px;
}

.control-button {
  width: 28px;
  height: 28px;
  border: none;
  border-radius: 6px;
  background: var(--bg-tertiary);
  color: var(--text-secondary);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  transition: all 0.2s ease;
}

.control-button:hover {
  background: var(--border-color);
  color: var(--text-primary);
  transform: scale(1.05);
}

/* 主容器 */
.main-container {
  flex: 1;
  display: flex;
  overflow: hidden;
}

/* 侧边栏 */
.sidebar {
  width: 280px;
  background: var(--gradient-sidebar);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border-right: 1px solid var(--border-color);
  display: flex;
  flex-direction: column;
  transition: width 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  overflow: hidden;
  box-shadow: 2px 0 8px var(--shadow-light);
}

/* sidebar.collapsed样式已移除 */

.sidebar-header {
  padding: 20px;
  border-bottom: 1px solid var(--border-color);
}

/* 智能中心标题样式 */
.intelligence-title {
  display: flex;
  align-items: center;
  gap: 12px;
  text-decoration: none;
  color: inherit;
  padding: 8px 12px;
  border-radius: 12px;
  transition: all 0.3s ease;
  background: linear-gradient(135deg,
    rgba(255, 107, 107, 0.05) 0%,
    rgba(254, 202, 87, 0.05) 25%,
    rgba(72, 219, 251, 0.05) 50%,
    rgba(255, 159, 243, 0.05) 75%,
    rgba(84, 160, 255, 0.05) 100%);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.intelligence-title:hover {
  background: linear-gradient(135deg,
    rgba(255, 107, 107, 0.08) 0%,
    rgba(254, 202, 87, 0.08) 25%,
    rgba(72, 219, 251, 0.08) 50%,
    rgba(255, 159, 243, 0.08) 75%,
    rgba(84, 160, 255, 0.08) 100%);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.intelligence-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border-radius: 8px;
  background: linear-gradient(135deg, #ff6b6b, #feca57, #48dbfb, #ff9ff3, #54a0ff);
  color: white;
  font-size: 16px;
}

.intelligence-text {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.title-main {
  font-size: 18px;
  font-weight: 700;
  margin: 0;
  background: linear-gradient(135deg, #ff6b6b, #feca57, #48dbfb, #ff9ff3, #54a0ff);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  animation: titleGlow 3s ease-in-out infinite;
}

.title-sub {
  font-size: 12px;
  color: var(--text-secondary);
  font-weight: 400;
  opacity: 0.8;
}

.intelligence-title .ai-badge {
  background: linear-gradient(135deg, #ff6b6b, #54a0ff);
  color: white;
  font-size: 10px;
  font-weight: 700;
  padding: 2px 6px;
  border-radius: 4px;
  animation: badgePulse 2s ease-in-out infinite;
}

/* 旧的logo相关样式已移除 */

/* collapse-button样式已移除 */

/* 导航菜单 */
.nav-menu {
  flex: 1;
  padding: 8px 16px;
  overflow-y: auto;
}

.nav-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 16px;
  margin: 2px 0;
  border-radius: 12px;
  text-decoration: none;
  color: var(--text-secondary);
  transition: all 0.2s ease;
  position: relative;
  min-height: 48px;
}

.nav-item:hover {
  background: var(--bg-tertiary);
  color: var(--text-primary);
  transform: translateX(2px);
}

.nav-item.active {
  background: var(--accent-blue);
  color: white;
  box-shadow: 0 4px 12px rgba(0, 122, 255, 0.3);
}

/* 智能中心特殊样式 */
.nav-item.special {
  position: relative;
  background: linear-gradient(135deg,
    rgba(255, 107, 107, 0.08) 0%,
    rgba(254, 202, 87, 0.08) 25%,
    rgba(72, 219, 251, 0.08) 50%,
    rgba(255, 159, 243, 0.08) 75%,
    rgba(84, 160, 255, 0.08) 100%);
  border: 1px solid rgba(255, 255, 255, 0.1);
  margin: 8px 0;
  overflow: hidden;
}

.nav-item.special::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: conic-gradient(
    from 0deg,
    rgba(255, 107, 107, 0.05) 0deg,
    rgba(254, 202, 87, 0.05) 72deg,
    rgba(72, 219, 251, 0.05) 144deg,
    rgba(255, 159, 243, 0.05) 216deg,
    rgba(84, 160, 255, 0.05) 288deg,
    rgba(255, 107, 107, 0.05) 360deg
  );
  animation: specialGradientRotate 20s linear infinite;
  z-index: -1;
}

.nav-item.special:hover {
  background: linear-gradient(135deg,
    rgba(255, 107, 107, 0.12) 0%,
    rgba(254, 202, 87, 0.12) 25%,
    rgba(72, 219, 251, 0.12) 50%,
    rgba(255, 159, 243, 0.12) 75%,
    rgba(84, 160, 255, 0.12) 100%);
  transform: translateX(4px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
}

.nav-item.special.active {
  background: linear-gradient(135deg,
    #ff6b6b 0%,
    #feca57 25%,
    #48dbfb 50%,
    #ff9ff3 75%,
    #54a0ff 100%);
  color: white;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
}

.featured-badge {
  position: absolute;
  top: 6px;
  right: 6px;
  background: linear-gradient(135deg, #ff6b6b, #54a0ff);
  border-radius: 8px;
  padding: 2px 6px;
  font-size: 10px;
  font-weight: 700;
  color: white;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
  animation: badgePulse 2s ease-in-out infinite;
}

.badge-text {
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.nav-icon {
  font-size: var(--icon-large);
  width: var(--icon-large);
  height: var(--icon-large);
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.nav-text {
  font-size: 14px;
  font-weight: 500;
  flex: 1;
}

.nav-description {
  font-size: 11px;
  opacity: 0.7;
  position: absolute;
  right: 16px;
  top: 50%;
  transform: translateY(-50%);
}

/* 侧边栏底部 - 优化布局版本 */
.sidebar-footer {
  padding: 4px 10px;
  border-top: 1px solid var(--border-color);
  background: var(--bg-tertiary);
}

.system-info {
  display: flex;
  flex-direction: column;
  gap: 1px;
}

/* 信息行基础样式 */
.info-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 9px;
  min-height: 11px;
  line-height: 1.2;
}

.left-info, .right-info {
  display: flex;
  align-items: center;
  gap: 6px;
}

.right-info {
  justify-content: flex-end;
}

/* 硬件信息行样式 */
.hardware-row {
  font-weight: 600;
}

.hardware-model {
  color: var(--text-primary);
  font-family: 'SF Mono', Monaco, monospace;
  font-weight: 700;
  letter-spacing: 0.5px;
}

.memory-info {
  color: var(--text-secondary);
  font-family: 'SF Mono', Monaco, monospace;
  font-weight: 500;
}

/* 系统信息行样式 */
.system-row {
  font-weight: 500;
}

.system-name {
  color: var(--text-primary);
  font-family: 'SF Mono', Monaco, monospace;
  font-weight: 600;
}

/* 时间信息行样式 */
.time-row {
  font-weight: 400;
}

.date-info {
  color: var(--text-secondary);
  font-family: 'SF Mono', Monaco, monospace;
  font-size: 8px;
  opacity: 0.8;
}

.ai-badge, .neural-badge {
  display: flex;
  align-items: center;
  gap: 2px;
  font-size: 9px;
  font-weight: 700;
  background: linear-gradient(135deg, #ff6b6b, #54a0ff);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.ai-icon, .neural-icon {
  font-size: 8px;
  filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.2));
  animation: iconPulse 2s ease-in-out infinite;
}

/* 主内容区域 */
.content-area {
  flex: 1;
  background: var(--bg-primary);
  overflow-y: auto;
  position: relative;
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: transparent;
}

::-webkit-scrollbar-thumb {
  background: var(--border-color);
  border-radius: 4px;
  transition: background 0.2s ease;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--text-secondary);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .sidebar {
    position: absolute;
    left: -280px;
    top: 0;
    bottom: 0;
    z-index: 999;
    transition: left 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .sidebar.show {
    left: 0;
  }

  /* sidebar.collapsed响应式样式已移除 */

  .title-bar-title {
    font-size: 12px;
  }

  .nav-description {
    display: none;
  }
}

/* 动画效果 */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.fade-in-up {
  animation: fadeInUp 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

@keyframes pulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
}

@keyframes specialGradientRotate {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

@keyframes badgePulse {
  0%, 100% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.1);
    opacity: 0.8;
  }
}

@keyframes iconPulse {
  0%, 100% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.1);
    opacity: 0.8;
  }
}

@keyframes logoSpin {
  0% {
    transform: rotate(0deg) scale(1);
    filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.3));
  }
  25% {
    transform: rotate(90deg) scale(1.05);
    filter: drop-shadow(0 3px 6px rgba(0, 0, 0, 0.35));
  }
  50% {
    transform: rotate(180deg) scale(1.1);
    filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.4));
  }
  75% {
    transform: rotate(270deg) scale(1.05);
    filter: drop-shadow(0 3px 6px rgba(0, 0, 0, 0.35));
  }
  100% {
    transform: rotate(360deg) scale(1);
    filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.3));
  }
}

@keyframes titleGlow {
  0%, 100% {
    filter: brightness(1);
    transform: scale(1);
  }
  50% {
    filter: brightness(1.1);
    transform: scale(1.02);
  }
}

.pulse {
  animation: pulse 2s infinite;
}

/* 主题切换动画 */
* {
  transition: background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease;
}

/* 苹果风格滚动条 */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.05);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.2);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 0, 0, 0.3);
}

/* 苹果风格按钮 */
.apple-button {
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 12px;
  padding: 12px 24px;
  font-size: 14px;
  font-weight: 500;
  color: #1d1d1f;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.apple-button:hover {
  background: rgba(255, 255, 255, 0.9);
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
}

.apple-button.primary {
  background: linear-gradient(135deg, #007AFF 0%, #5856D6 100%);
  color: white;
  border: none;
}

.apple-button.primary:hover {
  background: linear-gradient(135deg, #0056CC 0%, #4A4AC4 100%);
}

.apple-button.danger {
  background: linear-gradient(135deg, #FF3B30 0%, #FF6B6B 100%);
  color: white;
  border: none;
}

.apple-button.danger:hover {
  background: linear-gradient(135deg, #CC2E26 0%, #CC5555 100%);
}

/* 苹果风格卡片 */
.apple-card {
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 16px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.apple-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 16px 48px rgba(0, 0, 0, 0.15);
}

/* 苹果风格输入框 */
.apple-input {
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 12px;
  padding: 12px 16px;
  font-size: 14px;
  color: #1d1d1f;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.apple-input:focus {
  outline: none;
  border-color: #007AFF;
  box-shadow: 0 0 0 3px rgba(0, 122, 255, 0.1);
}

/* 苹果风格标签 */
.apple-tag {
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 8px;
  padding: 4px 12px;
  font-size: 12px;
  font-weight: 500;
  color: #1d1d1f;
}

.apple-tag.success {
  background: linear-gradient(135deg, #34C759 0%, #30D158 100%);
  color: white;
  border: none;
}

.apple-tag.warning {
  background: linear-gradient(135deg, #FF9500 0%, #FFB340 100%);
  color: white;
  border: none;
}

.apple-tag.error {
  background: linear-gradient(135deg, #FF3B30 0%, #FF6B6B 100%);
  color: white;
  border: none;
}

/* 毛玻璃效果 */
.glass {
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.3);
}

/* 动画效果 */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.fade-in-up {
  animation: fadeInUp 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

@keyframes pulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
}

.pulse {
  animation: pulse 2s infinite;
}
</style>
