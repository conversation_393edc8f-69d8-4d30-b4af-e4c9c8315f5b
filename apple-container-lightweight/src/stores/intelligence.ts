import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import type { 
  ImageRepository, 
  ImageCategory, 
  NewsArticle, 
  Tutorial, 
  LearningPath, 
  Discussion, 
  Tool 
} from '../api/intelligence'
import {
  getImageRepositories,
  getImageCategories,
  getNewsArticles,
  getTutorials,
  getLearningPaths,
  getDiscussions,
  getTools,
  searchIntelligenceContent
} from '../api/intelligence'

export const useIntelligenceStore = defineStore('intelligence', () => {
  // 状态
  const loading = ref(false)
  const error = ref<string | null>(null)
  
  // 镜像仓库
  const imageRepositories = ref<ImageRepository[]>([])
  const imageCategories = ref<ImageCategory[]>([])
  const selectedImageCategory = ref('all')
  
  // 资讯
  const newsArticles = ref<NewsArticle[]>([])
  const selectedNewsCategory = ref('all')
  
  // 教育资源
  const tutorials = ref<Tutorial[]>([])
  const learningPaths = ref<LearningPath[]>([])
  const selectedTutorialDifficulty = ref('all')
  
  // 社区
  const discussions = ref<Discussion[]>([])
  
  // 工具箱
  const tools = ref<Tool[]>([])
  const selectedToolCategory = ref('all')
  const toolSortBy = ref('popular')
  
  // 搜索
  const searchQuery = ref('')
  const searchResults = ref<any[]>([])
  
  // 收藏和历史
  const favorites = ref<any[]>([])
  const recentItems = ref<any[]>([])
  
  // 计算属性
  const filteredImageRepositories = computed(() => {
    let filtered = imageRepositories.value
    
    if (selectedImageCategory.value !== 'all') {
      filtered = filtered.filter(repo => repo.category === selectedImageCategory.value)
    }
    
    if (searchQuery.value) {
      const query = searchQuery.value.toLowerCase()
      filtered = filtered.filter(repo => 
        repo.name.toLowerCase().includes(query) ||
        repo.description.toLowerCase().includes(query) ||
        repo.tags.some(tag => tag.toLowerCase().includes(query))
      )
    }
    
    return filtered
  })
  
  const filteredNewsArticles = computed(() => {
    let filtered = newsArticles.value
    
    if (selectedNewsCategory.value !== 'all') {
      filtered = filtered.filter(article => article.category === selectedNewsCategory.value)
    }
    
    return filtered
  })
  
  const filteredTutorials = computed(() => {
    let filtered = tutorials.value
    
    if (selectedTutorialDifficulty.value !== 'all') {
      filtered = filtered.filter(tutorial => tutorial.difficulty === selectedTutorialDifficulty.value)
    }
    
    return filtered
  })
  
  const filteredTools = computed(() => {
    let filtered = tools.value
    
    if (selectedToolCategory.value !== 'all') {
      filtered = filtered.filter(tool => tool.category === selectedToolCategory.value)
    }
    
    // 排序
    switch (toolSortBy.value) {
      case 'newest':
        // 这里应该根据实际的创建时间排序
        break
      case 'rating':
        filtered = filtered.sort((a, b) => b.rating - a.rating)
        break
      case 'name':
        filtered = filtered.sort((a, b) => a.name.localeCompare(b.name))
        break
      case 'popular':
      default:
        filtered = filtered.sort((a, b) => b.users - a.users)
        break
    }
    
    return filtered
  })
  
  // Actions
  const setLoading = (value: boolean) => {
    loading.value = value
  }
  
  const setError = (message: string | null) => {
    error.value = message
  }
  
  // 镜像仓库相关
  const fetchImageRepositories = async (params?: any) => {
    try {
      setLoading(true)
      setError(null)
      const data = await getImageRepositories(params)
      imageRepositories.value = data
    } catch (err) {
      setError('获取镜像仓库失败')
      console.error('Failed to fetch image repositories:', err)
    } finally {
      setLoading(false)
    }
  }
  
  const fetchImageCategories = async () => {
    try {
      const data = await getImageCategories()
      imageCategories.value = data
    } catch (err) {
      console.error('Failed to fetch image categories:', err)
    }
  }
  
  const setSelectedImageCategory = (category: string) => {
    selectedImageCategory.value = category
  }
  
  // 资讯相关
  const fetchNewsArticles = async (params?: any) => {
    try {
      setLoading(true)
      setError(null)
      const data = await getNewsArticles(params)
      newsArticles.value = data
    } catch (err) {
      setError('获取新闻文章失败')
      console.error('Failed to fetch news articles:', err)
    } finally {
      setLoading(false)
    }
  }
  
  const setSelectedNewsCategory = (category: string) => {
    selectedNewsCategory.value = category
  }
  
  // 教育资源相关
  const fetchTutorials = async (params?: any) => {
    try {
      setLoading(true)
      setError(null)
      const data = await getTutorials(params)
      tutorials.value = data
    } catch (err) {
      setError('获取教程失败')
      console.error('Failed to fetch tutorials:', err)
    } finally {
      setLoading(false)
    }
  }
  
  const fetchLearningPaths = async () => {
    try {
      const data = await getLearningPaths()
      learningPaths.value = data
    } catch (err) {
      console.error('Failed to fetch learning paths:', err)
    }
  }
  
  const setSelectedTutorialDifficulty = (difficulty: string) => {
    selectedTutorialDifficulty.value = difficulty
  }
  
  // 社区相关
  const fetchDiscussions = async (params?: any) => {
    try {
      setLoading(true)
      setError(null)
      const data = await getDiscussions(params)
      discussions.value = data
    } catch (err) {
      setError('获取讨论失败')
      console.error('Failed to fetch discussions:', err)
    } finally {
      setLoading(false)
    }
  }
  
  // 工具箱相关
  const fetchTools = async (params?: any) => {
    try {
      setLoading(true)
      setError(null)
      const data = await getTools(params)
      tools.value = data
    } catch (err) {
      setError('获取工具失败')
      console.error('Failed to fetch tools:', err)
    } finally {
      setLoading(false)
    }
  }
  
  const setSelectedToolCategory = (category: string) => {
    selectedToolCategory.value = category
  }
  
  const setToolSortBy = (sortBy: string) => {
    toolSortBy.value = sortBy
  }
  
  // 搜索相关
  const setSearchQuery = (query: string) => {
    searchQuery.value = query
  }
  
  const performSearch = async (query: string, type?: string) => {
    try {
      setLoading(true)
      setError(null)
      const data = await searchIntelligenceContent(query, type)
      searchResults.value = data
    } catch (err) {
      setError('搜索失败')
      console.error('Failed to perform search:', err)
    } finally {
      setLoading(false)
    }
  }
  
  // 收藏相关
  const addToFavorites = (item: any) => {
    const exists = favorites.value.find(fav => fav.id === item.id && fav.type === item.type)
    if (!exists) {
      favorites.value.unshift({
        ...item,
        addedAt: new Date().toISOString()
      })
      
      // 限制收藏数量
      if (favorites.value.length > 50) {
        favorites.value = favorites.value.slice(0, 50)
      }
    }
  }
  
  const removeFromFavorites = (itemId: string, itemType: string) => {
    const index = favorites.value.findIndex(fav => fav.id === itemId && fav.type === itemType)
    if (index > -1) {
      favorites.value.splice(index, 1)
    }
  }
  
  const isFavorite = (itemId: string, itemType: string): boolean => {
    return favorites.value.some(fav => fav.id === itemId && fav.type === itemType)
  }
  
  // 历史记录相关
  const addToRecentItems = (item: any) => {
    // 移除已存在的相同项目
    const index = recentItems.value.findIndex(recent => recent.id === item.id && recent.type === item.type)
    if (index > -1) {
      recentItems.value.splice(index, 1)
    }
    
    // 添加到开头
    recentItems.value.unshift({
      ...item,
      accessedAt: new Date().toISOString()
    })
    
    // 限制历史记录数量
    if (recentItems.value.length > 20) {
      recentItems.value = recentItems.value.slice(0, 20)
    }
  }
  
  const clearRecentItems = () => {
    recentItems.value = []
  }
  
  // 初始化数据
  const initializeData = async () => {
    await Promise.all([
      fetchImageCategories(),
      fetchImageRepositories(),
      fetchNewsArticles(),
      fetchTutorials(),
      fetchLearningPaths(),
      fetchDiscussions(),
      fetchTools()
    ])
  }
  
  // 清理数据
  const clearData = () => {
    imageRepositories.value = []
    newsArticles.value = []
    tutorials.value = []
    discussions.value = []
    tools.value = []
    searchResults.value = []
    setError(null)
  }
  
  return {
    // 状态
    loading,
    error,
    
    // 镜像仓库
    imageRepositories,
    imageCategories,
    selectedImageCategory,
    filteredImageRepositories,
    
    // 资讯
    newsArticles,
    selectedNewsCategory,
    filteredNewsArticles,
    
    // 教育资源
    tutorials,
    learningPaths,
    selectedTutorialDifficulty,
    filteredTutorials,
    
    // 社区
    discussions,
    
    // 工具箱
    tools,
    selectedToolCategory,
    toolSortBy,
    filteredTools,
    
    // 搜索
    searchQuery,
    searchResults,
    
    // 收藏和历史
    favorites,
    recentItems,
    
    // Actions
    setLoading,
    setError,
    
    // 镜像仓库
    fetchImageRepositories,
    fetchImageCategories,
    setSelectedImageCategory,
    
    // 资讯
    fetchNewsArticles,
    setSelectedNewsCategory,
    
    // 教育资源
    fetchTutorials,
    fetchLearningPaths,
    setSelectedTutorialDifficulty,
    
    // 社区
    fetchDiscussions,
    
    // 工具箱
    fetchTools,
    setSelectedToolCategory,
    setToolSortBy,
    
    // 搜索
    setSearchQuery,
    performSearch,
    
    // 收藏
    addToFavorites,
    removeFromFavorites,
    isFavorite,
    
    // 历史记录
    addToRecentItems,
    clearRecentItems,
    
    // 初始化和清理
    initializeData,
    clearData
  }
})
