import { createRouter, createWebHistory } from 'vue-router'
import type { RouteRecordRaw } from 'vue-router'

// 导入页面组件
const Dashboard = () => import('../views/Dashboard.vue')
const Containers = () => import('../views/Containers.vue')
const Images = () => import('../views/Images.vue')
const Networks = () => import('../views/Networks.vue')
const Volumes = () => import('../views/Volumes.vue')
const Monitoring = () => import('../views/Monitoring.vue')
const Logs = () => import('../views/Logs.vue')
const Settings = () => import('../views/Settings.vue')
const IntelligenceCenter = () => import('../views/IntelligenceCenter.vue')

// 路由配置
const routes: RouteRecordRaw[] = [
  {
    path: '/',
    redirect: '/dashboard'
  },
  {
    path: '/dashboard',
    name: 'Dashboard',
    component: Dashboard,
    meta: {
      title: '仪表板',
      icon: 'ChartBarIcon',
      description: '系统概览和关键指标'
    }
  },
  {
    path: '/containers',
    name: 'Containers',
    component: Containers,
    meta: {
      title: '容器管理',
      icon: 'CubeIcon',
      description: '容器的创建、启动、停止和管理'
    }
  },
  {
    path: '/images',
    name: 'Images',
    component: Images,
    meta: {
      title: '镜像仓库',
      icon: 'PhotoIcon',
      description: 'Docker镜像的管理和构建'
    }
  },
  {
    path: '/networks',
    name: 'Networks',
    component: Networks,
    meta: {
      title: '网络管理',
      icon: 'GlobeAltIcon',
      description: 'Docker网络配置和管理'
    }
  },
  {
    path: '/volumes',
    name: 'Volumes',
    component: Volumes,
    meta: {
      title: '存储卷',
      icon: 'ServerIcon',
      description: '数据卷的创建和管理'
    }
  },
  {
    path: '/monitoring',
    name: 'Monitoring',
    component: Monitoring,
    meta: {
      title: '性能监控',
      icon: 'ChartLineIcon',
      description: '实时性能监控和资源使用情况'
    }
  },
  {
    path: '/logs',
    name: 'Logs',
    component: Logs,
    meta: {
      title: '日志中心',
      icon: 'DocumentTextIcon',
      description: '容器日志查看和搜索'
    }
  },
  {
    path: '/settings',
    name: 'Settings',
    component: Settings,
    meta: {
      title: '系统设置',
      icon: 'CogIcon',
      description: '应用配置和偏好设置'
    }
  },
  {
    path: '/intelligence-center',
    name: 'IntelligenceCenter',
    component: IntelligenceCenter,
    meta: {
      title: 'Apple Intelligence Center',
      icon: 'intelligence',
      description: '智能应用和资源中心'
    }
  },
  {
    path: '/:pathMatch(.*)*',
    name: 'NotFound',
    component: () => import('../views/NotFound.vue'),
    meta: {
      title: '页面未找到'
    }
  }
]

// 创建路由实例
const router = createRouter({
  history: createWebHistory(),
  routes,
  scrollBehavior(to, from, savedPosition) {
    if (savedPosition) {
      return savedPosition
    } else {
      return { top: 0 }
    }
  }
})

// 路由守卫
router.beforeEach((to, from, next) => {
  // 设置页面标题
  if (to.meta?.title) {
    document.title = `${to.meta.title} - Container Studio`
  } else {
    document.title = 'Container Studio'
  }
  
  next()
})

// 路由错误处理
router.onError((error) => {
  console.error('路由错误:', error)
})

export default router

// 导出路由配置供其他组件使用
export { routes }

// 导航菜单配置
export const navigationMenus = [
  {
    name: 'Dashboard',
    path: '/dashboard',
    icon: 'dashboard',
    title: '仪表板',
    description: '系统概览'
  },
  {
    name: 'Containers',
    path: '/containers',
    icon: 'containers',
    title: '容器',
    description: '容器管理'
  },
  {
    name: 'Images',
    path: '/images',
    icon: 'images',
    title: '镜像',
    description: '镜像仓库'
  },
  {
    name: 'Networks',
    path: '/networks',
    icon: 'networks',
    title: '网络',
    description: '网络管理'
  },
  {
    name: 'Volumes',
    path: '/volumes',
    icon: 'volumes',
    title: '存储',
    description: '数据卷'
  },
  {
    name: 'Monitoring',
    path: '/monitoring',
    icon: 'monitoring',
    title: '监控',
    description: '性能监控'
  },
  {
    name: 'Logs',
    path: '/logs',
    icon: 'logs',
    title: '日志',
    description: '日志中心'
  },
  {
    name: 'IntelligenceCenter',
    path: '/intelligence-center',
    icon: 'intelligence',
    title: '智能中心',
    description: 'AI资源中心',
    special: true,
    featured: true
  },
  {
    name: 'Settings',
    path: '/settings',
    icon: 'settings',
    title: '设置',
    description: '系统设置'
  }
]
