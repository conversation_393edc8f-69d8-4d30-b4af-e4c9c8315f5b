<template>
  <div class="container-management">
    <!-- 苹果风格顶部导航 -->
    <header class="apple-header glass fade-in-up">
      <div class="header-content">
        <div class="logo-section">
          <div class="apple-logo">🍎</div>
          <div class="title-section">
            <h1>Container Studio</h1>
            <p>Professional Docker Management</p>
          </div>
        </div>
        <div class="header-actions">
          <button class="apple-button" @click="refreshData">
            <span class="icon">🔄</span>
            刷新
          </button>
          <button class="apple-button primary" @click="createContainer">
            <span class="icon">➕</span>
            新建容器
          </button>
        </div>
      </div>
    </header>

    <main class="main-content">
      <!-- 苹果风格统计卡片 -->
      <section class="stats-section fade-in-up">
        <div class="stats-grid">
          <div class="stat-card apple-card">
            <div class="stat-icon success">
              <span>📦</span>
            </div>
            <div class="stat-content">
              <h3>{{ systemInfo.containerCount }}</h3>
              <p>运行容器</p>
            </div>
          </div>
          
          <div class="stat-card apple-card">
            <div class="stat-icon primary">
              <span>💿</span>
            </div>
            <div class="stat-content">
              <h3>{{ systemInfo.imageCount }}</h3>
              <p>可用镜像</p>
            </div>
          </div>
          
          <div class="stat-card apple-card">
            <div class="stat-icon warning">
              <span>⚙️</span>
            </div>
            <div class="stat-content">
              <h3>{{ systemInfo.version }}</h3>
              <p>系统版本</p>
            </div>
          </div>
        </div>
      </section>

      <!-- 苹果风格容器列表 -->
      <section class="container-section fade-in-up">
        <div class="section-header">
          <h2>容器列表</h2>
          <div class="search-box">
            <input type="text" placeholder="搜索容器..." class="apple-input" v-model="searchText">
          </div>
        </div>
        
        <div class="container-grid" v-if="!loading">
          <div v-for="container in filteredContainers" :key="container.id" class="container-item apple-card">
            <div class="container-header">
              <div class="container-name">
                <h3>{{ container.name }}</h3>
                <span :class="['status-badge', 'apple-tag', getStatusClass(container.status)]">
                  {{ container.status }}
                </span>
              </div>
              <div class="container-actions">
                <button class="apple-button" @click="viewLogs(container)" title="查看日志">
                  📄
                </button>
                <button class="apple-button primary" @click="startContainerAction(container)" title="启动容器">
                  ▶️
                </button>
                <button class="apple-button danger" @click="stopContainerAction(container)" title="停止容器">
                  ⏹️
                </button>
              </div>
            </div>
            <div class="container-info">
              <div class="info-item">
                <span class="label">端口:</span>
                <span class="value">{{ container.ports || 'N/A' }}</span>
              </div>
              <div class="info-item">
                <span class="label">镜像:</span>
                <span class="value">{{ container.image || 'N/A' }}</span>
              </div>
            </div>
          </div>
        </div>
        
        <div v-if="loading" class="loading-state">
          <div class="spinner pulse">⚡</div>
          <p>加载中...</p>
        </div>
      </section>

      <!-- 苹果风格镜像列表 -->
      <section class="images-section fade-in-up">
        <div class="section-header">
          <h2>镜像仓库</h2>
        </div>
        
        <div class="images-grid" v-if="!loading">
          <div v-for="image in imageList" :key="image.id" class="image-item apple-card">
            <div class="image-icon">💿</div>
            <div class="image-info">
              <h3>{{ image.name }}</h3>
              <p class="image-tag">{{ image.tag }}</p>
              <p class="image-size">{{ image.size }}</p>
            </div>
          </div>
        </div>
      </section>

      <!-- 苹果风格关于区域 -->
      <section class="about-section fade-in-up">
        <div class="about-card apple-card">
          <div class="about-content">
            <div class="about-icon">🍎</div>
            <h3>Container Studio</h3>
            <p>专业的Docker容器管理工具，采用苹果设计语言打造</p>
            <div class="feature-tags">
              <span class="feature-tag apple-tag">现代化UI</span>
              <span class="feature-tag apple-tag">毛玻璃效果</span>
              <span class="feature-tag apple-tag">响应式设计</span>
              <span class="feature-tag apple-tag">深色模式</span>
            </div>
            <div class="version-info">
              <span class="version">Version 1.0.0</span>
              <span class="copyright">© 2024 Apple Container Studio</span>
            </div>
          </div>
        </div>
      </section>
    </main>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import {
  getContainerListCompat,
  getImageListCompat,
  getSystemInfoCompat,
  startContainer,
  stopContainer,
  restartContainer,
  getContainerLogs
} from '../api/container'

interface Container {
  id: string
  name: string
  status: string
  ports?: string
  image?: string
}

interface Image {
  id: string
  name: string
  tag: string
  size: string
}

const loading = ref(false)
const searchText = ref('')
const containerList = ref<Container[]>([])
const imageList = ref<Image[]>([])
const systemInfo = ref({
  containerCount: 0,
  imageCount: 0,
  version: 'Unknown'
})

// 过滤容器列表
const filteredContainers = computed(() => {
  if (!searchText.value) return containerList.value
  return containerList.value.filter(container => 
    container.name.toLowerCase().includes(searchText.value.toLowerCase())
  )
})

const getStatusClass = (status: string) => {
  const statusMap: Record<string, string> = {
    running: 'success',
    stopped: 'error',
    paused: 'warning',
  }
  return statusMap[status] || ''
}

const loadData = async () => {
  loading.value = true
  try {
    const [containers, images, system] = await Promise.all([
      getContainerListCompat(),
      getImageListCompat(),
      getSystemInfoCompat(),
    ])
    containerList.value = containers
    imageList.value = images
    systemInfo.value = system
  } catch (error) {
    console.error('加载数据失败:', error)
  } finally {
    loading.value = false
  }
}

const refreshData = () => {
  loadData()
}

const createContainer = () => {
  console.log('创建新容器')
  // TODO: 实现创建容器功能
}

const viewLogs = async (container: Container) => {
  try {
    const logs = await getContainerLogs(container.id)
    // 这里可以打开一个模态框显示日志
    console.log('容器日志:', logs)
    alert(`容器 ${container.name} 的日志:\n\n${logs}`)
  } catch (error) {
    console.error('获取日志失败:', error)
    alert('获取日志失败')
  }
}

const startContainerAction = async (container: Container) => {
  try {
    const success = await startContainer(container.id)
    if (success) {
      console.log('容器启动成功:', container.name)
      await loadData() // 刷新数据
    } else {
      alert('启动容器失败')
    }
  } catch (error) {
    console.error('启动容器失败:', error)
    alert('启动容器失败')
  }
}

const stopContainerAction = async (container: Container) => {
  try {
    const success = await stopContainer(container.id)
    if (success) {
      console.log('容器停止成功:', container.name)
      await loadData() // 刷新数据
    } else {
      alert('停止容器失败')
    }
  } catch (error) {
    console.error('停止容器失败:', error)
    alert('停止容器失败')
  }
}

onMounted(() => {
  loadData()
})
</script>

<style scoped>
.container-management {
  min-height: 100vh;
  padding: 0;
  margin: 0;
}

/* 苹果风格顶部导航 */
.apple-header {
  padding: 20px 40px;
  margin: 20px 20px 0 20px;
  border-radius: 20px;
  position: sticky;
  top: 20px;
  z-index: 100;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  max-width: 1200px;
  margin: 0 auto;
}

.logo-section {
  display: flex;
  align-items: center;
  gap: 16px;
}

.apple-logo {
  font-size: 32px;
  animation: pulse 3s infinite;
}

.title-section h1 {
  margin: 0;
  font-size: 28px;
  font-weight: 700;
  background: linear-gradient(135deg, #007AFF 0%, #5856D6 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.title-section p {
  margin: 4px 0 0 0;
  font-size: 14px;
  color: #8E8E93;
  font-weight: 500;
}

.header-actions {
  display: flex;
  gap: 12px;
}

.header-actions .icon {
  margin-right: 8px;
}

/* 主内容区域 */
.main-content {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

/* 统计卡片区域 */
.stats-section {
  margin-bottom: 32px;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 20px;
}

.stat-card {
  padding: 24px;
  display: flex;
  align-items: center;
  gap: 20px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.stat-icon {
  width: 60px;
  height: 60px;
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
}

.stat-icon.success {
  background: linear-gradient(135deg, #34C759 0%, #30D158 100%);
}

.stat-icon.primary {
  background: linear-gradient(135deg, #007AFF 0%, #5856D6 100%);
}

.stat-icon.warning {
  background: linear-gradient(135deg, #FF9500 0%, #FFB340 100%);
}

.stat-content h3 {
  margin: 0;
  font-size: 32px;
  font-weight: 700;
  color: #1D1D1F;
}

.stat-content p {
  margin: 4px 0 0 0;
  font-size: 14px;
  color: #8E8E93;
  font-weight: 500;
}

/* 容器列表区域 */
.container-section, .images-section {
  margin-bottom: 32px;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.section-header h2 {
  margin: 0;
  font-size: 24px;
  font-weight: 700;
  color: #1D1D1F;
}

.search-box {
  width: 300px;
}

.search-box .apple-input {
  width: 100%;
}

.container-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 20px;
}

.container-item {
  padding: 20px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.container-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 16px;
}

.container-name h3 {
  margin: 0 0 8px 0;
  font-size: 18px;
  font-weight: 600;
  color: #1D1D1F;
}

.status-badge {
  font-size: 12px;
  padding: 4px 8px;
}

.container-actions {
  display: flex;
  gap: 8px;
}

.container-actions .apple-button {
  padding: 8px 12px;
  font-size: 16px;
  min-width: auto;
}

.container-info {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.info-item .label {
  font-size: 14px;
  color: #8E8E93;
  font-weight: 500;
}

.info-item .value {
  font-size: 14px;
  color: #1D1D1F;
  font-weight: 600;
  font-family: 'SF Mono', monospace;
}

/* 镜像列表区域 */
.images-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
}

.image-item {
  padding: 20px;
  text-align: center;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.image-icon {
  font-size: 48px;
  margin-bottom: 16px;
}

.image-info h3 {
  margin: 0 0 8px 0;
  font-size: 16px;
  font-weight: 600;
  color: #1D1D1F;
}

.image-tag {
  margin: 4px 0;
  font-size: 14px;
  color: #007AFF;
  font-weight: 500;
}

.image-size {
  margin: 4px 0 0 0;
  font-size: 12px;
  color: #8E8E93;
  font-family: 'SF Mono', monospace;
}

/* 加载状态 */
.loading-state {
  text-align: center;
  padding: 60px 20px;
}

.spinner {
  font-size: 48px;
  margin-bottom: 16px;
  display: inline-block;
}

.loading-state p {
  font-size: 16px;
  color: #8E8E93;
  margin: 0;
}

/* 关于区域 */
.about-section {
  margin-bottom: 32px;
}

.about-card {
  padding: 40px;
  text-align: center;
  max-width: 600px;
  margin: 0 auto;
}

.about-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
}

.about-icon {
  font-size: 64px;
  margin-bottom: 8px;
  animation: pulse 3s infinite;
}

.about-content h3 {
  margin: 0;
  font-size: 28px;
  font-weight: 700;
  background: linear-gradient(135deg, #007AFF 0%, #5856D6 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.about-content p {
  margin: 0;
  font-size: 16px;
  color: #8E8E93;
  line-height: 1.5;
  max-width: 400px;
}

.feature-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  justify-content: center;
  margin: 8px 0;
}

.feature-tag {
  font-size: 12px;
  padding: 6px 12px;
  background: linear-gradient(135deg, #007AFF 0%, #5856D6 100%);
  color: white;
  border: none;
}

.version-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
  margin-top: 8px;
}

.version {
  font-size: 14px;
  font-weight: 600;
  color: #007AFF;
}

.copyright {
  font-size: 12px;
  color: #8E8E93;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .apple-header {
    margin: 10px 10px 0 10px;
    padding: 16px 20px;
  }
  
  .header-content {
    flex-direction: column;
    gap: 16px;
    text-align: center;
  }
  
  .main-content {
    padding: 16px;
  }
  
  .stats-grid {
    grid-template-columns: 1fr;
  }
  
  .container-grid {
    grid-template-columns: 1fr;
  }
  
  .section-header {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }
  
  .search-box {
    width: 100%;
  }
  
  .container-header {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }
  
  .container-actions {
    justify-content: center;
  }
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
  .container-management {
    background: linear-gradient(135deg, #1c1c1e 0%, #2c2c2e 100%);
  }
  
  .title-section h1 {
    color: #FFFFFF;
    -webkit-text-fill-color: #FFFFFF;
  }
  
  .stat-content h3,
  .section-header h2,
  .container-name h3,
  .info-item .value,
  .image-info h3 {
    color: #FFFFFF;
  }
  
  .title-section p,
  .stat-content p,
  .info-item .label,
  .image-size,
  .loading-state p {
    color: #99999D;
  }
  
  .image-tag {
    color: #0A84FF;
  }
}
</style>      