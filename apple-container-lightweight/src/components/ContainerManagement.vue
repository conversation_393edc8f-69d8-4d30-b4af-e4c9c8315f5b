<template>
  <div class="container-management">
    <!-- 苹果风格顶部导航 -->
    <header class="apple-header glass fade-in-up">
      <div class="header-content">
        <div class="logo-section">
          <div class="apple-logo">🍎</div>
          <div class="title-section">
            <h1>Container Studio</h1>
            <p>Professional Docker Management</p>
          </div>
        </div>
        <div class="header-actions">
          <button class="apple-button" @click="refreshData">
            <span class="icon">🔄</span>
            刷新
          </button>
          <button class="apple-button primary" @click="createContainer">
            <span class="icon">➕</span>
            新建容器
          </button>
        </div>
      </div>
    </header>

    <main class="main-content">
      <!-- 苹果风格统计卡片 -->
      <section class="stats-section fade-in-up">
        <div class="stats-grid">
          <div class="stat-card apple-card">
            <div class="stat-icon success">
              <span>📦</span>
            </div>
            <div class="stat-content">
              <h3>{{ systemInfo.containerCount }}</h3>
              <p>运行容器</p>
            </div>
          </div>
          
          <div class="stat-card apple-card">
            <div class="stat-icon primary">
              <span>💿</span>
            </div>
            <div class="stat-content">
              <h3>{{ systemInfo.imageCount }}</h3>
              <p>可用镜像</p>
            </div>
          </div>
          
          <div class="stat-card apple-card">
            <div class="stat-icon warning">
              <span>⚙️</span>
            </div>
            <div class="stat-content">
              <h3>{{ systemInfo.version }}</h3>
              <p>系统版本</p>
            </div>
          </div>
        </div>
      </section>

      <!-- 苹果风格容器列表 -->
      <section class="container-section fade-in-up">
        <div class="section-header">
          <h2>容器列表</h2>
          <div class="search-box">
            <input type="text" placeholder="搜索容器..." class="apple-input" v-model="searchText">
          </div>
        </div>
        
        <div class="container-grid" v-if="!loading">
          <div v-for="container in filteredContainers" :key="container.id" class="container-item apple-card">
            <div class="container-header">
              <div class="container-name">
                <h3>{{ container.name }}</h3>
                <span :class="['status-badge', 'apple-tag', getStatusClass(container.status)]">
                  {{ container.status }}
                </span>
              </div>
              <div class="container-actions">
                <button class="apple-button" @click="viewLogs(container)" title="查看日志">
                  📄
                </button>
                <button class="apple-button primary" @click="startContainer(container)" title="启动容器">
                  ▶️
                </button>
                <button class="apple-button danger" @click="stopContainer(container)" title="停止容器">
                  ⏹️
                </button>
              </div>
            </div>
            <div class="container-info">
              <div class="info-item">
                <span class="label">端口:</span>
                <span class="value">{{ container.ports || 'N/A' }}</span>
              </div>
              <div class="info-item">
                <span class="label">镜像:</span>
                <span class="value">{{ container.image || 'N/A' }}</span>
              </div>
            </div>
          </div>
        </div>
        
        <div v-if="loading" class="loading-state">
          <div class="spinner pulse">⚡</div>
          <p>加载中...</p>
        </div>
      </section>

      <!-- 苹果风格镜像列表 -->
      <section class="images-section fade-in-up">
        <div class="section-header">
          <h2>镜像仓库</h2>
        </div>
        
        <div class="images-grid" v-if="!loading">
          <div v-for="image in imageList" :key="image.id" class="image-item apple-card">
            <div class="image-icon">💿</div>
            <div class="image-info">
              <h3>{{ image.name }}</h3>
              <p class="image-tag">{{ image.tag }}</p>
              <p class="image-size">{{ image.size }}</p>
            </div>
          </div>
        </div>
      </section>
    </main>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { getContainerList, getImageList, getSystemInfo } from '../api/container'

interface Container {
  id: string
  name: string
  status: string
  ports?: string
  image?: string
}

interface Image {
  id: string
  name: string
  tag: string
  size: string
}

const loading = ref(false)
const searchText = ref('')
const containerList = ref<Container[]>([])
const imageList = ref<Image[]>([])
const systemInfo = ref({
  containerCount: 0,
  imageCount: 0,
  version: 'Unknown'
})

// 过滤容器列表
const filteredContainers = computed(() => {
  if (!searchText.value) return containerList.value
  return containerList.value.filter(container => 
    container.name.toLowerCase().includes(searchText.value.toLowerCase())
  )
})

const getStatusClass = (status: string) => {
  const statusMap: Record<string, string> = {
    running: 'success',
    stopped: 'error',
    paused: 'warning',
  }
  return statusMap[status] || ''
}

const loadData = async () => {
  loading.value = true
  try {
    const [containers, images, system] = await Promise.all([
      getContainerList(),
      getImageList(),
      getSystemInfo(),
    ])
    containerList.value = containers
    imageList.value = images
    systemInfo.value = system
  } catch (error) {
    console.error('加载数据失败:', error)
  } finally {
    loading.value = false
  }
}

const refreshData = () => {
  loadData()
}

const createContainer = () => {
  console.log('创建新容器')
  // TODO: 实现创建容器功能
}

const viewLogs = (container: Container) => {
  console.log('查看日志:', container.name)
  // TODO: 实现查看日志功能
}

const startContainer = (container: Container) => {
  console.log('启动容器:', container.name)
  // TODO: 实现启动容器功能
}

const stopContainer = (container: Container) => {
  console.log('停止容器:', container.name)
  // TODO: 实现停止容器功能
}

onMounted(() => {
  loadData()
})
</script>

<style scoped>
.container-management {
  min-height: 100vh;
  background: #f0f2f5;
}

.header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 0 50px;
  height: auto;
  line-height: 1.5;
}

.header-content {
  text-align: center;
  color: white;
  padding: 20px 0;
}

.header-content h1 {
  margin: 0;
  font-size: 2em;
  font-weight: 700;
}

.header-content p {
  margin: 10px 0 0 0;
  opacity: 0.9;
}

.content {
  padding: 24px;
}

.stats-row {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 16px;
  margin-bottom: 24px;
}

.stats-card {
  text-align: center;
}

.container-card,
.image-card {
  margin-bottom: 24px;
}

.ant-card-head {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.ant-card-head-title {
  color: white;
}
</style>      