<template>
  <div class="apple-music-player" :class="{ 'player-expanded': expanded, 'player-mini': mini }">
    <!-- 毛玻璃背景 -->
    <div class="player-background">
      <div class="glass-layer"></div>
      <div class="gradient-overlay"></div>
    </div>
    
    <!-- 播放器内容 -->
    <div class="player-content">
      <!-- 专辑封面 -->
      <div class="album-cover" @click="toggleExpanded">
        <img 
          :src="currentTrack.cover" 
          :alt="currentTrack.title"
          class="cover-image"
        />
        <div class="cover-overlay">
          <div class="play-indicator" :class="{ 'playing': isPlaying }">
            <div class="wave-bar" v-for="i in 4" :key="i"></div>
          </div>
        </div>
      </div>
      
      <!-- 歌曲信息 -->
      <div class="track-info" v-if="!mini">
        <h4 class="track-title">{{ currentTrack.title }}</h4>
        <p class="track-artist">{{ currentTrack.artist }}</p>
        <p class="track-album" v-if="expanded">{{ currentTrack.album }}</p>
      </div>
      
      <!-- 控制按钮 -->
      <div class="player-controls">
        <button 
          class="control-btn control-btn-secondary" 
          @click="previousTrack"
          v-if="!mini"
        >
          <AppleIcon name="backward.fill" size="small" />
        </button>
        
        <button 
          class="control-btn control-btn-primary" 
          @click="togglePlay"
        >
          <AppleIcon 
            :name="isPlaying ? 'pause.fill' : 'play.fill'" 
            :size="mini ? 'small' : 'medium'" 
          />
        </button>
        
        <button 
          class="control-btn control-btn-secondary" 
          @click="nextTrack"
          v-if="!mini"
        >
          <AppleIcon name="forward.fill" size="small" />
        </button>
      </div>
      
      <!-- 进度条 -->
      <div class="progress-section" v-if="!mini">
        <div class="progress-bar" @click="seekTo">
          <div class="progress-track"></div>
          <div 
            class="progress-fill" 
            :style="{ width: `${progress}%` }"
          ></div>
          <div 
            class="progress-thumb" 
            :style="{ left: `${progress}%` }"
          ></div>
        </div>
        
        <div class="time-display" v-if="expanded">
          <span class="current-time">{{ formatTime(currentTime) }}</span>
          <span class="total-time">{{ formatTime(duration) }}</span>
        </div>
      </div>
      
      <!-- 音量控制 -->
      <div class="volume-section" v-if="expanded">
        <AppleIcon name="speaker.fill" size="small" />
        <div class="volume-bar" @click="setVolume">
          <div class="volume-track"></div>
          <div 
            class="volume-fill" 
            :style="{ width: `${volume * 100}%` }"
          ></div>
        </div>
        <AppleIcon name="speaker.wave.3.fill" size="small" />
      </div>
      
      <!-- 额外控制 -->
      <div class="extra-controls" v-if="expanded">
        <button class="control-btn control-btn-tertiary" @click="toggleShuffle">
          <AppleIcon 
            name="shuffle" 
            size="small" 
            :class="{ 'active': shuffle }"
          />
        </button>
        
        <button class="control-btn control-btn-tertiary" @click="toggleRepeat">
          <AppleIcon 
            :name="repeat === 'one' ? 'repeat.1' : 'repeat'" 
            size="small" 
            :class="{ 'active': repeat !== 'off' }"
          />
        </button>
        
        <button class="control-btn control-btn-tertiary" @click="toggleLike">
          <AppleIcon 
            :name="isLiked ? 'heart.fill' : 'heart'" 
            size="small" 
            :class="{ 'liked': isLiked }"
          />
        </button>
      </div>
    </div>
    
    <!-- 音频元素 -->
    <audio 
      ref="audioElement"
      :src="currentTrack.url"
      @timeupdate="updateProgress"
      @loadedmetadata="updateDuration"
      @ended="onTrackEnd"
    ></audio>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import AppleIcon from '../AppleIcon.vue'

interface Track {
  id: string
  title: string
  artist: string
  album: string
  cover: string
  url: string
  duration: number
}

interface Props {
  mini?: boolean
  tracks?: Track[]
}

const props = withDefaults(defineProps<Props>(), {
  mini: false,
  tracks: () => [
    {
      id: '1',
      title: 'Think Different',
      artist: 'Apple Inc.',
      album: 'Innovation',
      cover: 'https://images.unsplash.com/photo-1493225457124-a3eb161ffa5f?w=300&h=300&fit=crop',
      url: '/audio/think-different.mp3',
      duration: 180
    },
    {
      id: '2',
      title: 'Stay Hungry',
      artist: 'Steve Jobs',
      album: 'Stanford Commencement',
      cover: 'https://images.unsplash.com/photo-1514320291840-2e0a9bf2a9ae?w=300&h=300&fit=crop',
      url: '/audio/stay-hungry.mp3',
      duration: 240
    }
  ]
})

// 响应式状态
const expanded = ref(false)
const isPlaying = ref(false)
const currentTrackIndex = ref(0)
const currentTime = ref(0)
const duration = ref(0)
const volume = ref(0.7)
const shuffle = ref(false)
const repeat = ref<'off' | 'all' | 'one'>('off')
const isLiked = ref(false)

// 音频元素引用
const audioElement = ref<HTMLAudioElement>()

// 计算属性
const currentTrack = computed(() => props.tracks[currentTrackIndex.value])
const progress = computed(() => duration.value ? (currentTime.value / duration.value) * 100 : 0)

// 方法
const toggleExpanded = () => {
  expanded.value = !expanded.value
}

const togglePlay = () => {
  if (!audioElement.value) return
  
  if (isPlaying.value) {
    audioElement.value.pause()
  } else {
    audioElement.value.play()
  }
  isPlaying.value = !isPlaying.value
}

const previousTrack = () => {
  if (shuffle.value) {
    currentTrackIndex.value = Math.floor(Math.random() * props.tracks.length)
  } else {
    currentTrackIndex.value = currentTrackIndex.value > 0 
      ? currentTrackIndex.value - 1 
      : props.tracks.length - 1
  }
  if (isPlaying.value) {
    setTimeout(() => audioElement.value?.play(), 100)
  }
}

const nextTrack = () => {
  if (shuffle.value) {
    currentTrackIndex.value = Math.floor(Math.random() * props.tracks.length)
  } else {
    currentTrackIndex.value = currentTrackIndex.value < props.tracks.length - 1 
      ? currentTrackIndex.value + 1 
      : 0
  }
  if (isPlaying.value) {
    setTimeout(() => audioElement.value?.play(), 100)
  }
}

const seekTo = (event: MouseEvent) => {
  if (!audioElement.value) return
  
  const rect = (event.target as HTMLElement).getBoundingClientRect()
  const percent = (event.clientX - rect.left) / rect.width
  audioElement.value.currentTime = percent * duration.value
}

const setVolume = (event: MouseEvent) => {
  if (!audioElement.value) return
  
  const rect = (event.target as HTMLElement).getBoundingClientRect()
  const percent = (event.clientX - rect.left) / rect.width
  volume.value = Math.max(0, Math.min(1, percent))
  audioElement.value.volume = volume.value
}

const toggleShuffle = () => {
  shuffle.value = !shuffle.value
}

const toggleRepeat = () => {
  const modes: Array<'off' | 'all' | 'one'> = ['off', 'all', 'one']
  const currentIndex = modes.indexOf(repeat.value)
  repeat.value = modes[(currentIndex + 1) % modes.length]
}

const toggleLike = () => {
  isLiked.value = !isLiked.value
}

const updateProgress = () => {
  if (audioElement.value) {
    currentTime.value = audioElement.value.currentTime
  }
}

const updateDuration = () => {
  if (audioElement.value) {
    duration.value = audioElement.value.duration
  }
}

const onTrackEnd = () => {
  if (repeat.value === 'one') {
    audioElement.value?.play()
  } else if (repeat.value === 'all' || currentTrackIndex.value < props.tracks.length - 1) {
    nextTrack()
  } else {
    isPlaying.value = false
  }
}

const formatTime = (seconds: number): string => {
  const mins = Math.floor(seconds / 60)
  const secs = Math.floor(seconds % 60)
  return `${mins}:${secs.toString().padStart(2, '0')}`
}

// 生命周期
onMounted(() => {
  if (audioElement.value) {
    audioElement.value.volume = volume.value
  }
})

onUnmounted(() => {
  if (audioElement.value) {
    audioElement.value.pause()
  }
})
</script>

<style scoped>
.apple-music-player {
  position: relative;
  width: 100%;
  max-width: 320px;
  border-radius: 16px;
  overflow: hidden;
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 
    0 8px 32px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
}

.player-mini {
  max-width: 200px;
}

.player-expanded {
  max-width: 400px;
}

.player-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: -1;
}

.glass-layer {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, 
    rgba(255, 255, 255, 0.1) 0%,
    rgba(255, 255, 255, 0.05) 100%);
}

.gradient-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, 
    rgba(255, 107, 107, 0.05) 0%,
    rgba(254, 202, 87, 0.05) 25%,
    rgba(72, 219, 251, 0.05) 50%,
    rgba(255, 159, 243, 0.05) 75%,
    rgba(84, 160, 255, 0.05) 100%);
}

.player-content {
  padding: 16px;
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.album-cover {
  position: relative;
  width: 60px;
  height: 60px;
  border-radius: 8px;
  overflow: hidden;
  cursor: pointer;
  align-self: center;
  transition: transform 0.2s ease;
}

.player-expanded .album-cover {
  width: 120px;
  height: 120px;
  border-radius: 12px;
}

.album-cover:hover {
  transform: scale(1.05);
}

.cover-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.cover-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.3);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.2s ease;
}

.album-cover:hover .cover-overlay {
  opacity: 1;
}

.play-indicator {
  display: flex;
  gap: 2px;
  align-items: end;
}

.wave-bar {
  width: 3px;
  height: 12px;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 1px;
  animation: waveAnimation 1s ease-in-out infinite;
}

.playing .wave-bar:nth-child(1) { animation-delay: 0s; }
.playing .wave-bar:nth-child(2) { animation-delay: 0.1s; }
.playing .wave-bar:nth-child(3) { animation-delay: 0.2s; }
.playing .wave-bar:nth-child(4) { animation-delay: 0.3s; }

.track-info {
  text-align: center;
}

.track-title {
  font-size: 16px;
  font-weight: 600;
  margin: 0 0 4px 0;
  color: rgba(255, 255, 255, 0.9);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.track-artist {
  font-size: 14px;
  margin: 0 0 2px 0;
  color: rgba(255, 255, 255, 0.7);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.track-album {
  font-size: 12px;
  margin: 0;
  color: rgba(255, 255, 255, 0.5);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.player-controls {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 16px;
}

.control-btn {
  background: none;
  border: none;
  cursor: pointer;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.control-btn-primary {
  width: 48px;
  height: 48px;
  background: rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(10px);
}

.control-btn-primary:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: scale(1.1);
}

.control-btn-secondary {
  width: 36px;
  height: 36px;
  background: rgba(255, 255, 255, 0.1);
}

.control-btn-secondary:hover {
  background: rgba(255, 255, 255, 0.2);
}

.control-btn-tertiary {
  width: 32px;
  height: 32px;
  background: rgba(255, 255, 255, 0.05);
}

.control-btn-tertiary:hover {
  background: rgba(255, 255, 255, 0.1);
}

.progress-section {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.progress-bar {
  position: relative;
  height: 4px;
  cursor: pointer;
}

.progress-track {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 2px;
}

.progress-fill {
  position: absolute;
  top: 0;
  left: 0;
  bottom: 0;
  background: linear-gradient(90deg, #ff6b6b, #54a0ff);
  border-radius: 2px;
  transition: width 0.1s ease;
}

.progress-thumb {
  position: absolute;
  top: -4px;
  width: 12px;
  height: 12px;
  background: #ffffff;
  border-radius: 50%;
  transform: translateX(-50%);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  opacity: 0;
  transition: opacity 0.2s ease;
}

.progress-bar:hover .progress-thumb {
  opacity: 1;
}

.time-display {
  display: flex;
  justify-content: space-between;
  font-size: 12px;
  color: rgba(255, 255, 255, 0.6);
}

.volume-section {
  display: flex;
  align-items: center;
  gap: 12px;
}

.volume-bar {
  flex: 1;
  height: 4px;
  position: relative;
  cursor: pointer;
}

.volume-track {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 2px;
}

.volume-fill {
  position: absolute;
  top: 0;
  left: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.6);
  border-radius: 2px;
}

.extra-controls {
  display: flex;
  justify-content: center;
  gap: 16px;
}

.active {
  color: #54a0ff !important;
}

.liked {
  color: #ff6b6b !important;
}

@keyframes waveAnimation {
  0%, 100% { height: 8px; }
  50% { height: 16px; }
}

/* 深色主题适配 */
@media (prefers-color-scheme: dark) {
  .glass-layer {
    background: linear-gradient(135deg, 
      rgba(255, 255, 255, 0.05) 0%,
      rgba(255, 255, 255, 0.02) 100%);
  }
  
  .apple-music-player {
    border-color: rgba(255, 255, 255, 0.1);
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .apple-music-player {
    max-width: 280px;
  }
  
  .player-expanded {
    max-width: 320px;
  }
}
</style>
