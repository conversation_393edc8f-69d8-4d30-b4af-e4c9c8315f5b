<template>
  <div class="performance-chart-container">
    <div class="chart-header">
      <h3 class="chart-title">{{ title }}</h3>
      <div class="chart-controls">
        <button 
          v-for="period in timePeriods" 
          :key="period.value"
          class="period-button"
          :class="{ active: selectedPeriod === period.value }"
          @click="selectedPeriod = period.value"
        >
          {{ period.label }}
        </button>
      </div>
    </div>
    
    <div class="chart-wrapper">
      <v-chart 
        class="chart" 
        :option="chartOption" 
        :autoresize="true"
        @click="handleChartClick"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { use } from 'echarts/core'
import { CanvasRenderer } from 'echarts/renderers'
import { LineChart, BarChart } from 'echarts/charts'
import {
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  GridComponent,
  DataZoomComponent
} from 'echarts/components'
import VChart, { THEME_KEY } from 'vue-echarts'
import { provide } from 'vue'

// 提供主题
provide(THEME_KEY, 'dark')

// 注册必要的组件
use([
  CanvasRenderer,
  LineChart,
  BarChart,
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  GridComponent,
  DataZoomComponent
])

interface Props {
  title: string
  type: 'cpu' | 'memory' | 'network' | 'containers'
  data?: any[]
}

const props = withDefaults(defineProps<Props>(), {
  title: '性能监控',
  type: 'cpu',
  data: () => []
})

const selectedPeriod = ref('1h')
const timePeriods = [
  { label: '1小时', value: '1h' },
  { label: '6小时', value: '6h' },
  { label: '24小时', value: '24h' },
  { label: '7天', value: '7d' }
]

// 生成模拟数据
const generateMockData = (type: string, period: string) => {
  const now = new Date()
  const points = period === '1h' ? 60 : period === '6h' ? 72 : period === '24h' ? 144 : 168
  const interval = period === '1h' ? 60000 : period === '6h' ? 300000 : period === '24h' ? 600000 : 3600000
  
  const data = []
  const categories = []
  
  for (let i = points; i >= 0; i--) {
    const time = new Date(now.getTime() - i * interval)
    categories.push(time.toLocaleTimeString('zh-CN', { 
      hour: '2-digit', 
      minute: '2-digit',
      ...(period === '7d' && { month: '2-digit', day: '2-digit' })
    }))
    
    let value = 0
    switch (type) {
      case 'cpu':
        value = Math.random() * 80 + 10
        break
      case 'memory':
        value = Math.random() * 60 + 20
        break
      case 'network':
        value = Math.random() * 100 + 10
        break
      case 'containers':
        value = Math.floor(Math.random() * 3) + 2
        break
    }
    
    data.push(Math.round(value * 100) / 100)
  }
  
  return { data, categories }
}

// 图表配置
const chartOption = computed(() => {
  const { data, categories } = generateMockData(props.type, selectedPeriod.value)
  
  const baseOption = {
    backgroundColor: 'transparent',
    tooltip: {
      trigger: 'axis',
      backgroundColor: 'rgba(0, 0, 0, 0.8)',
      borderColor: 'transparent',
      textStyle: {
        color: '#ffffff',
        fontSize: 12
      },
      formatter: (params: any) => {
        const param = params[0]
        const unit = props.type === 'containers' ? '个' : props.type === 'network' ? 'MB/s' : '%'
        return `${param.name}<br/>${param.seriesName}: ${param.value}${unit}`
      }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      top: '10%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: categories,
      axisLine: {
        lineStyle: {
          color: 'var(--border-color)'
        }
      },
      axisLabel: {
        color: 'var(--text-secondary)',
        fontSize: 11
      }
    },
    yAxis: {
      type: 'value',
      axisLine: {
        show: false
      },
      axisTick: {
        show: false
      },
      axisLabel: {
        color: 'var(--text-secondary)',
        fontSize: 11,
        formatter: (value: number) => {
          const unit = props.type === 'containers' ? '' : props.type === 'network' ? 'MB/s' : '%'
          return `${value}${unit}`
        }
      },
      splitLine: {
        lineStyle: {
          color: 'var(--border-color)',
          type: 'dashed'
        }
      }
    }
  }
  
  // 根据类型设置不同的图表样式
  switch (props.type) {
    case 'cpu':
      return {
        ...baseOption,
        series: [{
          name: 'CPU使用率',
          type: 'line',
          smooth: true,
          data: data,
          lineStyle: {
            color: '#ff6b6b',
            width: 2
          },
          areaStyle: {
            color: {
              type: 'linear',
              x: 0, y: 0, x2: 0, y2: 1,
              colorStops: [
                { offset: 0, color: 'rgba(255, 107, 107, 0.3)' },
                { offset: 1, color: 'rgba(255, 107, 107, 0.05)' }
              ]
            }
          },
          symbol: 'circle',
          symbolSize: 4,
          itemStyle: {
            color: '#ff6b6b'
          }
        }]
      }
      
    case 'memory':
      return {
        ...baseOption,
        series: [{
          name: '内存使用率',
          type: 'line',
          smooth: true,
          data: data,
          lineStyle: {
            color: '#007aff',
            width: 2
          },
          areaStyle: {
            color: {
              type: 'linear',
              x: 0, y: 0, x2: 0, y2: 1,
              colorStops: [
                { offset: 0, color: 'rgba(0, 122, 255, 0.3)' },
                { offset: 1, color: 'rgba(0, 122, 255, 0.05)' }
              ]
            }
          },
          symbol: 'circle',
          symbolSize: 4,
          itemStyle: {
            color: '#007aff'
          }
        }]
      }
      
    case 'network':
      return {
        ...baseOption,
        series: [{
          name: '网络流量',
          type: 'line',
          smooth: true,
          data: data,
          lineStyle: {
            color: '#34c759',
            width: 2
          },
          areaStyle: {
            color: {
              type: 'linear',
              x: 0, y: 0, x2: 0, y2: 1,
              colorStops: [
                { offset: 0, color: 'rgba(52, 199, 89, 0.3)' },
                { offset: 1, color: 'rgba(52, 199, 89, 0.05)' }
              ]
            }
          },
          symbol: 'circle',
          symbolSize: 4,
          itemStyle: {
            color: '#34c759'
          }
        }]
      }
      
    case 'containers':
      return {
        ...baseOption,
        series: [{
          name: '运行容器数',
          type: 'bar',
          data: data,
          itemStyle: {
            color: {
              type: 'linear',
              x: 0, y: 0, x2: 0, y2: 1,
              colorStops: [
                { offset: 0, color: '#ff9500' },
                { offset: 1, color: '#ffb340' }
              ]
            },
            borderRadius: [2, 2, 0, 0]
          },
          barWidth: '60%'
        }]
      }
      
    default:
      return baseOption
  }
})

const handleChartClick = (params: any) => {
  console.log('图表点击事件:', params)
}

// 自动刷新数据
let refreshInterval: NodeJS.Timeout | null = null

onMounted(() => {
  // 每30秒刷新一次数据
  refreshInterval = setInterval(() => {
    // 触发重新计算
    selectedPeriod.value = selectedPeriod.value
  }, 30000)
})

onUnmounted(() => {
  if (refreshInterval) {
    clearInterval(refreshInterval)
  }
})
</script>

<style scoped>
.performance-chart-container {
  background: var(--glass-bg);
  backdrop-filter: blur(20px);
  border: 1px solid var(--border-color);
  border-radius: 16px;
  padding: 24px;
  height: 400px;
  display: flex;
  flex-direction: column;
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.chart-title {
  font-size: 18px;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0;
}

.chart-controls {
  display: flex;
  gap: 8px;
}

.period-button {
  padding: 6px 12px;
  border: 1px solid var(--border-color);
  border-radius: 6px;
  background: transparent;
  color: var(--text-secondary);
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.period-button:hover {
  background: var(--bg-tertiary);
  color: var(--text-primary);
}

.period-button.active {
  background: var(--accent-blue);
  color: white;
  border-color: var(--accent-blue);
}

.chart-wrapper {
  flex: 1;
  min-height: 0;
}

.chart {
  width: 100%;
  height: 100%;
}
</style>
