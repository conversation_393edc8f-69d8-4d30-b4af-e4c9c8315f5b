<template>
  <div 
    class="apple-icon" 
    :class="[`size-${size}`, `variant-${variant}`, { active }]"
    :style="{ color: customColor }"
  >
    <svg 
      :width="iconSize" 
      :height="iconSize" 
      viewBox="0 0 24 24" 
      fill="none" 
      xmlns="http://www.w3.org/2000/svg"
    >
      <g v-html="iconPath"></g>
    </svg>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'

interface Props {
  name: string
  size?: 'small' | 'medium' | 'large' | 'xlarge'
  variant?: 'regular' | 'fill' | 'light'
  active?: boolean
  customColor?: string
}

const props = withDefaults(defineProps<Props>(), {
  size: 'medium',
  variant: 'regular',
  active: false
})

// 图标尺寸映射
const sizeMap = {
  small: 16,
  medium: 20,
  large: 24,
  xlarge: 32
}

const iconSize = computed(() => sizeMap[props.size])

// SF Symbols风格的图标路径
const iconPaths: Record<string, string> = {
  // 导航图标
  dashboard: `
    <path stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" 
          d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2 2z"/>
    <path stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" 
          d="M8 21V7a2 2 0 012-2h4a2 2 0 012 2v14"/>
  `,
  
  containers: `
    <path stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" 
          d="M21 16V8a2 2 0 00-1-1.73l-7-4a2 2 0 00-2 0l-7 4A2 2 0 003 8v8a2 2 0 001 1.73l7 4a2 2 0 002 0l7-4A2 2 0 0021 16z"/>
    <path stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" 
          d="M3.27 6.96L12 12.01l8.73-5.05"/>
    <path stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" 
          d="M12 22.08V12"/>
  `,
  
  images: `
    <circle stroke="currentColor" stroke-width="1.5" cx="12" cy="12" r="10"/>
    <path stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" 
          d="M12 6v6l4 2"/>
  `,
  
  networks: `
    <path stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" 
          d="M16 4h2a2 2 0 012 2v12a2 2 0 01-2 2h-2"/>
    <path stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" 
          d="M8 20H6a2 2 0 01-2-2V6a2 2 0 012-2h2"/>
    <path stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" 
          d="M12 16v4"/>
    <path stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" 
          d="M12 4v4"/>
    <circle stroke="currentColor" stroke-width="1.5" cx="12" cy="12" r="4"/>
  `,
  
  volumes: `
    <path stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" 
          d="M14 2H6a2 2 0 00-2 2v16a2 2 0 002 2h12a2 2 0 002-2V8z"/>
    <path stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" 
          d="M14 2v6h6"/>
    <path stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" 
          d="M16 13H8"/>
    <path stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" 
          d="M16 17H8"/>
    <path stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" 
          d="M10 9H8"/>
  `,
  
  monitoring: `
    <path stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" 
          d="M3 3v18h18"/>
    <path stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" 
          d="M7 12l4-4 4 4 4-4"/>
  `,
  
  logs: `
    <path stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" 
          d="M14 2H6a2 2 0 00-2 2v16a2 2 0 002 2h12a2 2 0 002-2V8z"/>
    <path stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" 
          d="M14 2v6h6"/>
    <path stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" 
          d="M16 13H8"/>
    <path stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" 
          d="M16 17H8"/>
  `,
  
  settings: `
    <circle stroke="currentColor" stroke-width="1.5" cx="12" cy="12" r="3"/>
    <path stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" 
          d="M19.4 15a1.65 1.65 0 00.33 1.82l.06.06a2 2 0 010 2.83 2 2 0 01-2.83 0l-.06-.06a1.65 1.65 0 00-1.82-.33 1.65 1.65 0 00-1 1.51V21a2 2 0 01-2 2 2 2 0 01-2-2v-.09A1.65 1.65 0 009 19.4a1.65 1.65 0 00-1.82.33l-.06.06a2 2 0 01-2.83 0 2 2 0 010-2.83l.06-.06a1.65 1.65 0 00.33-1.82 1.65 1.65 0 00-1.51-1H3a2 2 0 01-2-2 2 2 0 012-2h.09A1.65 1.65 0 004.6 9a1.65 1.65 0 00-.33-1.82l-.06-.06a2 2 0 010-2.83 2 2 0 012.83 0l.06.06a1.65 1.65 0 001.82.33H9a1.65 1.65 0 001-1.51V3a2 2 0 012-2 2 2 0 012 2v.09a1.65 1.65 0 001 1.51 1.65 1.65 0 001.82-.33l.06-.06a2 2 0 012.83 0 2 2 0 010 2.83l-.06.06a1.65 1.65 0 00-.33 1.82V9a1.65 1.65 0 001.51 1H21a2 2 0 012 2 2 2 0 01-2 2h-.09a1.65 1.65 0 00-1.51 1z"/>
  `,
  
  // 功能图标
  refresh: `
    <path stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" 
          d="M1 4v6h6"/>
    <path stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" 
          d="M3.51 15a9 9 0 102.13-9.36L1 10"/>
  `,
  
  play: `
    <path stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" 
          d="M8 5v14l11-7z"/>
  `,
  
  stop: `
    <rect stroke="currentColor" stroke-width="1.5" x="6" y="6" width="12" height="12" rx="2"/>
  `,
  
  pause: `
    <path stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" 
          d="M6 4h4v16H6z"/>
    <path stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" 
          d="M14 4h4v16h-4z"/>
  `,
  
  plus: `
    <path stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" 
          d="M12 5v14"/>
    <path stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" 
          d="M5 12h14"/>
  `,
  
  search: `
    <circle stroke="currentColor" stroke-width="1.5" cx="11" cy="11" r="8"/>
    <path stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" 
          d="M21 21l-4.35-4.35"/>
  `,
  
  // 系统图标
  cpu: `
    <rect stroke="currentColor" stroke-width="1.5" x="4" y="4" width="16" height="16" rx="2"/>
    <rect stroke="currentColor" stroke-width="1.5" x="9" y="9" width="6" height="6"/>
    <path stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" 
          d="M9 1v3"/>
    <path stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" 
          d="M15 1v3"/>
    <path stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" 
          d="M9 20v3"/>
    <path stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" 
          d="M15 20v3"/>
    <path stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" 
          d="M20 9h3"/>
    <path stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" 
          d="M20 14h3"/>
    <path stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" 
          d="M1 9h3"/>
    <path stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" 
          d="M1 14h3"/>
  `,
  
  memory: `
    <path stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" 
          d="M6 19V5a2 2 0 012-2h8a2 2 0 012 2v14"/>
    <path stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" 
          d="M6 19a2 2 0 002 2h8a2 2 0 002-2"/>
    <path stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" 
          d="M6 9h12"/>
    <path stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" 
          d="M6 15h12"/>
  `,
  
  apple: `
    <path stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"
          d="M12 20.94c1.5 0 2.75 1.06 4 1.06 3 0 6-8 6-12.22A4.91 4.91 0 0017 5c-2.22 0-4 1.44-5 2-1-.56-2.78-2-5-2a4.9 4.9 0 00-5 4.78C2 14 5 22 8 22c1.25 0 2.5-1.06 4-1.06z"/>
    <path stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"
          d="M10.2 6.34c.8-.47 1.6-.94 2.8-.94 2.5 0 2.5 2.94 0 2.94-.6 0-1.2-.47-2-.47-.8 0-1.4.47-2 .47-2.5 0-2.5-2.94 0-2.94.4 0 .8.47 1.2.94z"/>
  `,

  // 主题切换图标
  sun: `
    <circle stroke="currentColor" stroke-width="1.5" cx="12" cy="12" r="5"/>
    <path stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"
          d="M12 1v2"/>
    <path stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"
          d="M12 21v2"/>
    <path stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"
          d="M4.22 4.22l1.42 1.42"/>
    <path stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"
          d="M18.36 18.36l1.42 1.42"/>
    <path stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"
          d="M1 12h2"/>
    <path stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"
          d="M21 12h2"/>
    <path stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"
          d="M4.22 19.78l1.42-1.42"/>
    <path stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"
          d="M18.36 5.64l1.42-1.42"/>
  `,

  moon: `
    <path stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"
          d="M21 12.79A9 9 0 1111.21 3 7 7 0 0021 12.79z"/>
  `,

  // 方向图标
  'chevron-left': `
    <path stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"
          d="M15 18l-6-6 6-6"/>
  `,

  'chevron-right': `
    <path stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"
          d="M9 18l6-6-6-6"/>
  `,

  'chevron-up': `
    <path stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"
          d="M18 15l-6-6-6 6"/>
  `,

  'chevron-down': `
    <path stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"
          d="M6 9l6 6 6-6"/>
  `,

  // 删除图标
  trash: `
    <path stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"
          d="M3 6h18"/>
    <path stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"
          d="M19 6v14a2 2 0 01-2 2H7a2 2 0 01-2-2V6m3 0V4a2 2 0 012-2h4a2 2 0 012 2v2"/>
    <path stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"
          d="M10 11v6"/>
    <path stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"
          d="M14 11v6"/>
  `,

  // Intelligence Center 专用图标
  intelligence: `
    <path stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"
          d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
    <circle stroke="currentColor" stroke-width="1.5" cx="12" cy="12" r="3"/>
  `,

  brain: `
    <path stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"
          d="M9.5 2A2.5 2.5 0 0 0 7 4.5v.5H6a2 2 0 0 0-2 2v2a2 2 0 0 0 2 2h1v.5A2.5 2.5 0 0 0 9.5 14h.5v1a2 2 0 0 0 2 2h2a2 2 0 0 0 2-2v-1h.5a2.5 2.5 0 0 0 2.5-2.5V11h1a2 2 0 0 0 2-2V7a2 2 0 0 0-2-2h-1v-.5A2.5 2.5 0 0 0 16.5 2h-7z"/>
  `,

  sparkles: `
    <path stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"
          d="M9.937 15.5A2 2 0 0 0 8.5 14.063l-6.135-1.582a.5.5 0 0 1 0-.962L8.5 9.936A2 2 0 0 0 9.937 8.5l1.582-6.135a.5.5 0 0 1 .962 0L14.063 8.5A2 2 0 0 0 15.5 9.937l6.135 1.582a.5.5 0 0 1 0 .962L15.5 14.063a2 2 0 0 0-1.437 1.437l-1.582 6.135a.5.5 0 0 1-.962 0L9.937 15.5z"/>
  `,

  library: `
    <path stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"
          d="M4 19.5A2.5 2.5 0 0 1 6.5 17H20"/>
    <path stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"
          d="M6.5 2H20v20H6.5A2.5 2.5 0 0 1 4 19.5v-15A2.5 2.5 0 0 1 6.5 2z"/>
    <path stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"
          d="M8 7h8"/>
    <path stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"
          d="M8 11h8"/>
  `,

  news: `
    <path stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"
          d="M4 22h16a2 2 0 0 0 2-2V4a2 2 0 0 0-2-2H8a2 2 0 0 0-2 2v16a2 2 0 0 1-2 2Zm0 0a2 2 0 0 1-2-2v-9c0-1.1.9-2 2-2h2"/>
    <path stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"
          d="M18 14h-8"/>
    <path stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"
          d="M15 18h-5"/>
    <path stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"
          d="M10 6h8v4h-8V6z"/>
  `,

  graduation: `
    <path stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"
          d="M22 10v6M2 10l10-5 10 5-10 5z"/>
    <path stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"
          d="M6 12v5c3 3 9 3 12 0v-5"/>
  `,

  community: `
    <path stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"
          d="M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"/>
    <circle stroke="currentColor" stroke-width="1.5" cx="9" cy="7" r="4"/>
    <path stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"
          d="M23 21v-2a4 4 0 0 0-3-3.87"/>
    <path stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"
          d="M16 3.13a4 4 0 0 1 0 7.75"/>
  `,

  toolbox: `
    <path stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"
          d="M16 4h2a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2V6a2 2 0 0 1 2-2h2"/>
    <rect stroke="currentColor" stroke-width="1.5" x="8" y="2" width="8" height="4" rx="1" ry="1"/>
    <path stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"
          d="M9 12h6"/>
    <path stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"
          d="M9 16h6"/>
  `,

  filter: `
    <path stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"
          d="M22 3H2l8 9.46V19l4 2v-8.54L22 3z"/>
  `,

  star: `
    <path stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"
          d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
  `,

  heart: `
    <path stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"
          d="M20.84 4.61a5.5 5.5 0 0 0-7.78 0L12 5.67l-1.06-1.06a5.5 5.5 0 0 0-7.78 7.78l1.06 1.06L12 21.23l7.78-7.78 1.06-1.06a5.5 5.5 0 0 0 0-7.78z"/>
  `,

  download: `
    <path stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"
          d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"/>
    <path stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"
          d="M7 10l5 5 5-5"/>
    <path stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"
          d="M12 15V3"/>
  `,

  // 更多图标
  x: `
    <path stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"
          d="M18 6L6 18"/>
    <path stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"
          d="M6 6l12 12"/>
  `,

  fire: `
    <path stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"
          d="M8.5 14.5A2.5 2.5 0 0 0 11 12c0-1.38-.5-2-1-3-1.072-2.143-.224-4.054 2-6 .5 2.5 2 4.9 4 6.5 2 1.6 3 3.5 3 5.5a7 7 0 1 1-14 0c0-1.153.433-2.294 1-3a2.5 2.5 0 0 0 2.5 2.5z"/>
  `,

  gift: `
    <path stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"
          d="M20 12v10H4V12"/>
    <path stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"
          d="M22 7H2v5h20V7z"/>
    <path stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"
          d="M12 22V7"/>
    <path stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"
          d="M12 7H7.5a2.5 2.5 0 0 1 0-5C11 2 12 7 12 7z"/>
    <path stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"
          d="M12 7h4.5a2.5 2.5 0 0 0 0-5C13 2 12 7 12 7z"/>
  `,

  help: `
    <circle stroke="currentColor" stroke-width="1.5" cx="12" cy="12" r="10"/>
    <path stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"
          d="M9.09 9a3 3 0 0 1 5.83 1c0 2-3 3-3 3"/>
    <path stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"
          d="M12 17h.01"/>
  `,

  message: `
    <path stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"
          d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"/>
  `,

  chat: `
    <path stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"
          d="M21 11.5a8.38 8.38 0 0 1-.9 3.8 8.5 8.5 0 0 1-7.6 4.7 8.38 8.38 0 0 1-3.8-.9L3 21l1.9-5.7a8.38 8.38 0 0 1-.9-3.8 8.5 8.5 0 0 1 4.7-7.6 8.38 8.38 0 0 1 3.8-.9h.5a8.48 8.48 0 0 1 8 8v.5z"/>
  `,

  eye: `
    <path stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"
          d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"/>
    <circle stroke="currentColor" stroke-width="1.5" cx="12" cy="12" r="3"/>
  `,

  users: `
    <path stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"
          d="M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"/>
    <circle stroke="currentColor" stroke-width="1.5" cx="9" cy="7" r="4"/>
    <path stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"
          d="M23 21v-2a4 4 0 0 0-3-3.87"/>
    <path stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"
          d="M16 3.13a4 4 0 0 1 0 7.75"/>
  `,

  clock: `
    <circle stroke="currentColor" stroke-width="1.5" cx="12" cy="12" r="10"/>
    <path stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"
          d="M12 6v6l4 2"/>
  `,

  bookmark: `
    <path stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"
          d="M19 21l-7-5-7 5V5a2 2 0 0 1 2-2h10a2 2 0 0 1 2 2z"/>
  `,

  share: `
    <circle stroke="currentColor" stroke-width="1.5" cx="18" cy="5" r="3"/>
    <circle stroke="currentColor" stroke-width="1.5" cx="6" cy="12" r="3"/>
    <circle stroke="currentColor" stroke-width="1.5" cx="18" cy="19" r="3"/>
    <path stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"
          d="M8.59 13.51l6.83 3.98"/>
    <path stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"
          d="M15.41 6.51l-6.82 3.98"/>
  `,

  grid: `
    <rect stroke="currentColor" stroke-width="1.5" x="3" y="3" width="7" height="7"/>
    <rect stroke="currentColor" stroke-width="1.5" x="14" y="3" width="7" height="7"/>
    <rect stroke="currentColor" stroke-width="1.5" x="14" y="14" width="7" height="7"/>
    <rect stroke="currentColor" stroke-width="1.5" x="3" y="14" width="7" height="7"/>
  `,

  list: `
    <path stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"
          d="M8 6h13"/>
    <path stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"
          d="M8 12h13"/>
    <path stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"
          d="M8 18h13"/>
    <path stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"
          d="M3 6h.01"/>
    <path stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"
          d="M3 12h.01"/>
    <path stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"
          d="M3 18h.01"/>
  `,

  trending: `
    <path stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"
          d="M23 6l-9.5 9.5-5-5L1 18"/>
    <path stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"
          d="M17 6h6v6"/>
  `,

  check: `
    <path stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"
          d="M20 6L9 17l-5-5"/>
  `,

  shield: `
    <path stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"
          d="M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10z"/>
  `,

  database: `
    <ellipse stroke="currentColor" stroke-width="1.5" cx="12" cy="5" rx="9" ry="3"/>
    <path stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"
          d="M21 12c0 1.66-4 3-9 3s-9-1.34-9-3"/>
    <path stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"
          d="M3 5v14c0 1.66 4 3 9 3s9-1.34 9-3V5"/>
  `,

  server: `
    <rect stroke="currentColor" stroke-width="1.5" x="2" y="3" width="20" height="4" rx="1"/>
    <rect stroke="currentColor" stroke-width="1.5" x="2" y="10" width="20" height="4" rx="1"/>
    <rect stroke="currentColor" stroke-width="1.5" x="2" y="17" width="20" height="4" rx="1"/>
    <path stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"
          d="M6 5h.01"/>
    <path stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"
          d="M6 12h.01"/>
    <path stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"
          d="M6 19h.01"/>
  `,

  'external-link': `
    <path stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"
          d="M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6"/>
    <path stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"
          d="M15 3h6v6"/>
    <path stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"
          d="M10 14L21 3"/>
  `,

  edit: `
    <path stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"
          d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7"/>
    <path stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"
          d="M18.5 2.5a2.121 2.121 0 0 1 3 3L12 15l-4 1 1-4 9.5-9.5z"/>
  `,

  link: `
    <path stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"
          d="M10 13a5 5 0 0 0 7.54.54l3-3a5 5 0 0 0-7.07-7.07l-1.72 1.71"/>
    <path stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"
          d="M14 11a5 5 0 0 0-7.54-.54l-3 3a5 5 0 0 0 7.07 7.07l1.71-1.71"/>
  `,

  api: `
    <rect stroke="currentColor" stroke-width="1.5" x="14" y="2" width="4" height="20" rx="2"/>
    <rect stroke="currentColor" stroke-width="1.5" x="6" y="2" width="4" height="20" rx="2"/>
  `,

  github: `
    <path stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"
          d="M9 19c-5 1.5-5-2.5-7-3m14 6v-3.87a3.37 3.37 0 0 0-.94-2.61c3.14-.35 6.44-1.54 6.44-7A5.44 5.44 0 0 0 20 4.77 5.07 5.07 0 0 0 19.91 1S18.73.65 16 2.48a13.38 13.38 0 0 0-7 0C6.27.65 5.09 1 5.09 1A5.07 5.07 0 0 0 5 4.77a5.44 5.44 0 0 0-1.5 3.78c0 5.42 3.3 6.61 6.44 7A3.37 3.37 0 0 0 9 18.13V22"/>
  `,

  document: `
    <path stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"
          d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"/>
    <path stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"
          d="M14 2v6h6"/>
    <path stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"
          d="M16 13H8"/>
    <path stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"
          d="M16 17H8"/>
    <path stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"
          d="M10 9H8"/>
  `,

  pipeline: `
    <circle stroke="currentColor" stroke-width="1.5" cx="12" cy="12" r="3"/>
    <path stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"
          d="M12 1v6m0 6v6"/>
    <path stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"
          d="M21 12h-6m-6 0H3"/>
  `
}

const iconPath = computed(() => {
  return iconPaths[props.name] || iconPaths.dashboard
})
</script>

<style scoped>
.apple-icon {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  color: var(--text-secondary);
}

.apple-icon svg {
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

/* 尺寸变体 */
.apple-icon.size-small {
  width: 16px;
  height: 16px;
}

.apple-icon.size-medium {
  width: 20px;
  height: 20px;
}

.apple-icon.size-large {
  width: 24px;
  height: 24px;
}

.apple-icon.size-xlarge {
  width: 32px;
  height: 32px;
}

/* 样式变体 */
.apple-icon.variant-regular {
  color: var(--text-secondary);
}

.apple-icon.variant-fill {
  color: var(--text-primary);
}

.apple-icon.variant-light {
  color: var(--text-tertiary);
}

/* 激活状态 */
.apple-icon.active {
  color: var(--accent-blue);
  transform: scale(1.05);
}

/* 悬停效果 */
.apple-icon:hover {
  color: var(--text-primary);
  transform: scale(1.1);
}

.apple-icon.active:hover {
  color: var(--accent-blue);
  transform: scale(1.15);
}

/* 深色主题优化 */
:root.dark .apple-icon {
  filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.3));
}

/* 动画效果 */
@keyframes iconPulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
}

.apple-icon.pulse {
  animation: iconPulse 2s infinite;
}
</style>
