<template>
  <div class="pixel-apple-logo" ref="containerRef">
    <canvas 
      ref="canvasRef" 
      :width="canvasWidth" 
      :height="canvasHeight"
      @click="restartAnimation"
    ></canvas>
    <div class="logo-overlay">
      <div class="inspiration-text">
        <p>每个像素都是一位伟大的贡献者</p>
        <p class="subtitle">开源精神，乔布斯传承</p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, nextTick } from 'vue'

// 组件引用
const containerRef = ref<HTMLDivElement>()
const canvasRef = ref<HTMLCanvasElement>()

// Canvas 尺寸
const canvasWidth = ref(800)
const canvasHeight = ref(600)

// 动画状态
let animationId: number | null = null
let ctx: CanvasRenderingContext2D | null = null
let particles: Particle[] = []
let startTime = 0

// 粒子类
class Particle {
  x: number
  y: number
  targetX: number
  targetY: number
  vx: number
  vy: number
  size: number
  rotation: number
  rotationSpeed: number
  opacity: number
  phase: number
  imageIndex: number
  arrived: boolean

  constructor(targetX: number, targetY: number) {
    // 随机起始位置（屏幕边缘）
    const side = Math.floor(Math.random() * 4)
    switch (side) {
      case 0: // 顶部
        this.x = Math.random() * canvasWidth.value
        this.y = -20
        break
      case 1: // 右侧
        this.x = canvasWidth.value + 20
        this.y = Math.random() * canvasHeight.value
        break
      case 2: // 底部
        this.x = Math.random() * canvasWidth.value
        this.y = canvasHeight.value + 20
        break
      case 3: // 左侧
        this.x = -20
        this.y = Math.random() * canvasHeight.value
        break
    }

    this.targetX = targetX
    this.targetY = targetY
    this.vx = 0
    this.vy = 0
    this.size = 6 + Math.random() * 4
    this.rotation = Math.random() * Math.PI * 2
    this.rotationSpeed = (Math.random() - 0.5) * 0.02
    this.opacity = 0
    this.phase = Math.random() * Math.PI * 2
    this.imageIndex = Math.floor(Math.random() * jobsImages.length)
    this.arrived = false
  }

  update(deltaTime: number, currentTime: number) {
    // 淡入效果
    if (this.opacity < 1) {
      this.opacity += deltaTime * 0.002
    }

    // 计算到目标的距离
    const dx = this.targetX - this.x
    const dy = this.targetY - this.y
    const distance = Math.sqrt(dx * dx + dy * dy)

    if (distance > 2) {
      // 汇聚阶段 - 使用缓动函数
      const force = 0.02
      this.vx += dx * force
      this.vy += dy * force
      
      // 添加阻尼
      this.vx *= 0.95
      this.vy *= 0.95
      
      this.x += this.vx
      this.y += this.vy
    } else {
      // 到达目标位置
      this.arrived = true
      this.x = this.targetX
      this.y = this.targetY
      
      // 轻微的浮动效果
      const floatX = Math.sin(currentTime * 0.001 + this.phase) * 0.5
      const floatY = Math.cos(currentTime * 0.0015 + this.phase) * 0.3
      this.x += floatX
      this.y += floatY
    }

    // 旋转
    this.rotation += this.rotationSpeed
  }

  draw() {
    if (!ctx) return

    ctx.save()
    ctx.globalAlpha = this.opacity
    ctx.translate(this.x, this.y)
    ctx.rotate(this.rotation)

    // 绘制乔布斯头像
    const img = jobsImages[this.imageIndex]
    if (img.complete) {
      ctx.drawImage(img, -this.size/2, -this.size/2, this.size, this.size)
    } else {
      // 备用方案：绘制彩色圆点
      ctx.fillStyle = `hsl(${this.imageIndex * 60}, 70%, 60%)`
      ctx.beginPath()
      ctx.arc(0, 0, this.size/2, 0, Math.PI * 2)
      ctx.fill()
    }

    ctx.restore()
  }
}

// 乔布斯头像图片数组
const jobsImages: HTMLImageElement[] = []

// 苹果形状坐标数据（简化版）
const appleShape = [
  // 苹果主体
  ...generateAppleBody(),
  // 叶子
  ...generateAppleLeaf()
]

function generateAppleBody(): Array<{x: number, y: number}> {
  const points: Array<{x: number, y: number}> = []
  const centerX = canvasWidth.value / 2
  const centerY = canvasHeight.value / 2 + 20

  // 苹果主体 - 使用心形曲线变形
  for (let t = 0; t < Math.PI * 2; t += 0.05) {
    // 心形曲线的变形版本，创造苹果形状
    const scale = 60
    let x = scale * (16 * Math.sin(t) ** 3)
    let y = -scale * (13 * Math.cos(t) - 5 * Math.cos(2*t) - 2 * Math.cos(3*t) - Math.cos(4*t))

    // 调整为苹果形状
    x = x * 0.8
    y = y * 0.6 + 40

    // 转换到画布坐标
    x += centerX
    y += centerY

    points.push({x, y})
  }

  // 添加更多密集的点来填充苹果内部
  for (let y = centerY - 80; y < centerY + 120; y += 8) {
    for (let x = centerX - 100; x < centerX + 100; x += 8) {
      if (isInsideApple(x, y, centerX, centerY)) {
        points.push({x, y})
      }
    }
  }

  return points
}

// 判断点是否在苹果形状内部
function isInsideApple(x: number, y: number, centerX: number, centerY: number): boolean {
  const dx = x - centerX
  const dy = y - centerY - 20

  // 苹果主体检测
  const appleEquation = (dx/80)**2 + ((dy-20)/100)**2
  const isInMainBody = appleEquation < 1

  // 被咬的部分检测
  const biteX = centerX + 45
  const biteY = centerY - 30
  const biteDx = x - biteX
  const biteDy = y - biteY
  const biteRadius = 35
  const isInBite = (biteDx**2 + biteDy**2) < biteRadius**2

  return isInMainBody && !isInBite
}

function generateAppleLeaf(): Array<{x: number, y: number}> {
  const points: Array<{x: number, y: number}> = []
  const centerX = canvasWidth.value / 2 + 15
  const centerY = canvasHeight.value / 2 - 80

  // 叶子形状 - 使用椭圆变形
  for (let t = 0; t < Math.PI * 2; t += 0.2) {
    const a = 20 // 长轴
    const b = 8  // 短轴

    // 椭圆参数方程，然后旋转45度
    let x = a * Math.cos(t)
    let y = b * Math.sin(t)

    // 旋转45度
    const cos45 = Math.cos(Math.PI / 4)
    const sin45 = Math.sin(Math.PI / 4)
    const rotatedX = x * cos45 - y * sin45
    const rotatedY = x * sin45 + y * cos45

    points.push({
      x: centerX + rotatedX,
      y: centerY + rotatedY
    })
  }

  // 叶子茎部
  for (let i = 0; i < 8; i++) {
    points.push({
      x: centerX - i * 2,
      y: centerY + i * 3
    })
  }

  return points
}

// 初始化乔布斯头像
function initJobsImages() {
  const jobsSvgs = [
    // 乔布斯头像变体1 - 经典侧脸
    `<svg width="16" height="16" viewBox="0 0 16 16" xmlns="http://www.w3.org/2000/svg">
      <defs>
        <radialGradient id="face1" cx="0.3" cy="0.3">
          <stop offset="0%" stop-color="#fdbcb4"/>
          <stop offset="100%" stop-color="#e8a598"/>
        </radialGradient>
      </defs>
      <circle cx="8" cy="8" r="7" fill="url(#face1)" stroke="#d4a574" stroke-width="0.5"/>
      <ellipse cx="6" cy="7" rx="1" ry="0.5" fill="#333"/>
      <path d="M5 9 Q8 11 9 9" stroke="#333" stroke-width="0.5" fill="none"/>
      <path d="M4 5 Q6 3 8 5" stroke="#8b4513" stroke-width="0.8" fill="none"/>
    </svg>`,

    // 乔布斯头像变体2 - 正面
    `<svg width="16" height="16" viewBox="0 0 16 16" xmlns="http://www.w3.org/2000/svg">
      <defs>
        <radialGradient id="face2" cx="0.5" cy="0.3">
          <stop offset="0%" stop-color="#fdbcb4"/>
          <stop offset="100%" stop-color="#e8a598"/>
        </radialGradient>
      </defs>
      <circle cx="8" cy="8" r="7" fill="url(#face2)" stroke="#d4a574" stroke-width="0.5"/>
      <circle cx="6" cy="7" r="0.8" fill="#333"/>
      <circle cx="10" cy="7" r="0.8" fill="#333"/>
      <ellipse cx="8" cy="9" rx="1" ry="0.5" fill="#d4a574"/>
      <path d="M6 11 Q8 12 10 11" stroke="#333" stroke-width="0.5" fill="none"/>
    </svg>`,

    // 乔布斯头像变体3 - 思考状
    `<svg width="16" height="16" viewBox="0 0 16 16" xmlns="http://www.w3.org/2000/svg">
      <defs>
        <radialGradient id="face3" cx="0.4" cy="0.3">
          <stop offset="0%" stop-color="#fdbcb4"/>
          <stop offset="100%" stop-color="#e8a598"/>
        </radialGradient>
      </defs>
      <circle cx="8" cy="8" r="7" fill="url(#face3)" stroke="#d4a574" stroke-width="0.5"/>
      <ellipse cx="6.5" cy="7" rx="0.8" ry="0.6" fill="#333"/>
      <ellipse cx="9.5" cy="7" rx="0.8" ry="0.6" fill="#333"/>
      <path d="M7 10 Q8 11 9 10" stroke="#333" stroke-width="0.5" fill="none"/>
      <path d="M5 4 Q7 2 9 4" stroke="#8b4513" stroke-width="1" fill="none"/>
    </svg>`
  ]

  jobsSvgs.forEach((svgString) => {
    const img = new Image()
    const blob = new Blob([svgString], { type: 'image/svg+xml' })
    const url = URL.createObjectURL(blob)
    img.onload = () => URL.revokeObjectURL(url)
    img.src = url
    jobsImages.push(img)
  })
}

// 创建粒子
function createParticles() {
  particles = []

  // 分批创建粒子，创造波浪式出现效果
  appleShape.forEach((point, index) => {
    const delay = Math.random() * 2000 + index * 5 // 随机延迟 + 顺序延迟
    setTimeout(() => {
      if (particles.length < appleShape.length) { // 防止重复创建
        particles.push(new Particle(point.x, point.y))
      }
    }, delay)
  })
}

// 动画循环
function animate(currentTime: number) {
  if (!ctx) return
  
  if (startTime === 0) startTime = currentTime
  const deltaTime = currentTime - startTime
  
  // 清空画布
  ctx.fillStyle = 'rgba(0, 0, 0, 0.02)'
  ctx.fillRect(0, 0, canvasWidth.value, canvasHeight.value)
  
  // 更新和绘制粒子
  particles.forEach(particle => {
    particle.update(deltaTime, currentTime)
    particle.draw()
  })
  
  animationId = requestAnimationFrame(animate)
}

// 重启动画
function restartAnimation() {
  particles = []
  startTime = 0
  createParticles()
}

// 初始化
function init() {
  nextTick(() => {
    if (!canvasRef.value) return
    
    ctx = canvasRef.value.getContext('2d')
    if (!ctx) return
    
    // 设置画布样式
    ctx.imageSmoothingEnabled = true
    
    // 初始化图片和粒子
    initJobsImages()
    createParticles()
    
    // 开始动画
    animationId = requestAnimationFrame(animate)
  })
}

// 生命周期
onMounted(() => {
  init()
})

onUnmounted(() => {
  if (animationId) {
    cancelAnimationFrame(animationId)
  }
})
</script>

<style scoped>
.pixel-apple-logo {
  position: relative;
  width: 100%;
  height: 600px;
  background: radial-gradient(circle at center, 
    rgba(0, 0, 0, 0.9) 0%, 
    rgba(0, 0, 0, 0.95) 100%);
  border-radius: 20px;
  overflow: hidden;
  cursor: pointer;
  transition: all 0.3s ease;
}

.pixel-apple-logo:hover {
  transform: scale(1.02);
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
}

canvas {
  width: 100%;
  height: 100%;
  display: block;
}

.logo-overlay {
  position: absolute;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
  text-align: center;
  color: white;
  pointer-events: none;
}

.inspiration-text p {
  margin: 4px 0;
  font-family: 'SF Pro Display', -apple-system, BlinkMacSystemFont, sans-serif;
  font-weight: 500;
  text-shadow: 0 2px 10px rgba(0, 0, 0, 0.5);
}

.inspiration-text .subtitle {
  font-size: 14px;
  opacity: 0.8;
  font-style: italic;
}

@media (max-width: 768px) {
  .pixel-apple-logo {
    height: 400px;
  }
  
  .inspiration-text p {
    font-size: 14px;
  }
  
  .inspiration-text .subtitle {
    font-size: 12px;
  }
}
</style>
