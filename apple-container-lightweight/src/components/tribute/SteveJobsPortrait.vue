<template>
  <div class="steve-jobs-portrait" :class="{ 'portrait-large': large }">
    <!-- 手绘风格的乔布斯头像 -->
    <div class="portrait-container">
      <svg 
        class="portrait-svg" 
        viewBox="0 0 200 240" 
        xmlns="http://www.w3.org/2000/svg"
      >
        <!-- 背景光晕 -->
        <defs>
          <radialGradient id="backgroundGlow" cx="50%" cy="40%" r="60%">
            <stop offset="0%" style="stop-color:#ffffff;stop-opacity:0.1"/>
            <stop offset="100%" style="stop-color:#000000;stop-opacity:0.05"/>
          </radialGradient>
          
          <!-- 苹果渐变 -->
          <linearGradient id="appleGradient" x1="0%" y1="0%" x2="100%" y2="100%">
            <stop offset="0%" style="stop-color:#ff6b6b"/>
            <stop offset="25%" style="stop-color:#feca57"/>
            <stop offset="50%" style="stop-color:#48dbfb"/>
            <stop offset="75%" style="stop-color:#ff9ff3"/>
            <stop offset="100%" style="stop-color:#54a0ff"/>
          </linearGradient>
          
          <!-- 头发渐变 -->
          <linearGradient id="hairGradient" x1="0%" y1="0%" x2="100%" y2="100%">
            <stop offset="0%" style="stop-color:#4a4a4a"/>
            <stop offset="100%" style="stop-color:#2c2c2c"/>
          </linearGradient>
          
          <!-- 皮肤渐变 -->
          <linearGradient id="skinGradient" x1="0%" y1="0%" x2="100%" y2="100%">
            <stop offset="0%" style="stop-color:#f4c2a1"/>
            <stop offset="100%" style="stop-color:#e8a87c"/>
          </linearGradient>
        </defs>
        
        <!-- 背景 -->
        <circle cx="100" cy="120" r="90" fill="url(#backgroundGlow)"/>
        
        <!-- 头部轮廓 -->
        <ellipse cx="100" cy="130" rx="45" ry="55" fill="url(#skinGradient)" stroke="#d4a574" stroke-width="1"/>
        
        <!-- 头发 -->
        <path d="M 55 95 Q 100 75 145 95 Q 140 85 135 80 Q 100 70 65 80 Q 60 85 55 95 Z" 
              fill="url(#hairGradient)" stroke="#333" stroke-width="0.5"/>
        
        <!-- 眼镜框架 -->
        <g class="glasses">
          <!-- 左镜片 -->
          <circle cx="85" cy="115" r="12" fill="none" stroke="#333" stroke-width="2"/>
          <!-- 右镜片 -->
          <circle cx="115" cy="115" r="12" fill="none" stroke="#333" stroke-width="2"/>
          <!-- 鼻梁 -->
          <line x1="97" y1="115" x2="103" y2="115" stroke="#333" stroke-width="2"/>
          <!-- 左镜腿 -->
          <line x1="73" y1="115" x2="65" y2="118" stroke="#333" stroke-width="2"/>
          <!-- 右镜腿 -->
          <line x1="127" y1="115" x2="135" y2="118" stroke="#333" stroke-width="2"/>
        </g>
        
        <!-- 眼睛 -->
        <circle cx="85" cy="115" r="3" fill="#2c3e50"/>
        <circle cx="115" cy="115" r="3" fill="#2c3e50"/>
        <circle cx="86" cy="113" r="1" fill="#ffffff"/>
        <circle cx="116" cy="113" r="1" fill="#ffffff"/>
        
        <!-- 鼻子 -->
        <path d="M 100 125 L 98 135 L 102 135 Z" fill="#d4a574" stroke="#c49968" stroke-width="0.5"/>
        
        <!-- 嘴巴 -->
        <path d="M 92 145 Q 100 150 108 145" fill="none" stroke="#c49968" stroke-width="1.5" stroke-linecap="round"/>
        
        <!-- 胡须轮廓 -->
        <path d="M 85 140 Q 100 155 115 140" fill="none" stroke="#999" stroke-width="0.5" opacity="0.6"/>
        
        <!-- 衣领 -->
        <path d="M 70 185 Q 100 175 130 185 L 130 240 L 70 240 Z" fill="#2c3e50"/>
        
        <!-- 苹果 (在手中) -->
        <g class="apple-in-hand" transform="translate(130, 160)">
          <!-- 苹果主体 -->
          <path d="M 0 15 Q -8 5 -5 -2 Q 0 -8 8 -5 Q 15 0 12 8 Q 8 15 0 15 Z" 
                fill="url(#appleGradient)" stroke="#ff4757" stroke-width="0.5"/>
          <!-- 苹果叶子 -->
          <path d="M 3 -5 Q 8 -8 6 -3" fill="#2ed573" stroke="#20bf6b" stroke-width="0.3"/>
          <!-- 咬痕 -->
          <path d="M 8 2 Q 12 5 8 8" fill="#ffffff" opacity="0.8"/>
        </g>
        
        <!-- 手臂和手 -->
        <path d="M 130 185 Q 140 170 145 160 Q 148 155 145 150" 
              fill="none" stroke="url(#skinGradient)" stroke-width="8" stroke-linecap="round"/>
        
        <!-- 签名风格的线条 -->
        <g class="sketch-lines" opacity="0.3">
          <path d="M 60 100 Q 80 95 100 100" fill="none" stroke="#666" stroke-width="0.3"/>
          <path d="M 100 100 Q 120 95 140 100" fill="none" stroke="#666" stroke-width="0.3"/>
          <path d="M 75 160 Q 100 155 125 160" fill="none" stroke="#666" stroke-width="0.3"/>
        </g>
      </svg>
    </div>
    
    <!-- 动态光效 -->
    <div class="portrait-glow"></div>
  </div>
</template>

<script setup lang="ts">
interface Props {
  large?: boolean
}

defineProps<Props>()
</script>

<style scoped>
.steve-jobs-portrait {
  position: relative;
  width: 120px;
  height: 144px;
  margin: 0 auto;
  transition: all 0.3s ease;
}

.portrait-large {
  width: 160px;
  height: 192px;
}

.portrait-container {
  position: relative;
  width: 100%;
  height: 100%;
  border-radius: 20px;
  overflow: hidden;
  background: linear-gradient(135deg, 
    rgba(255, 255, 255, 0.1) 0%, 
    rgba(255, 255, 255, 0.05) 100%);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 
    0 8px 32px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

.portrait-svg {
  width: 100%;
  height: 100%;
  filter: drop-shadow(0 2px 8px rgba(0, 0, 0, 0.1));
}

.portrait-glow {
  position: absolute;
  top: -10px;
  left: -10px;
  right: -10px;
  bottom: -10px;
  background: conic-gradient(
    from 0deg,
    #ff6b6b,
    #feca57,
    #48dbfb,
    #ff9ff3,
    #54a0ff,
    #ff6b6b
  );
  border-radius: 25px;
  opacity: 0;
  z-index: -1;
  animation: rotate 8s linear infinite;
  transition: opacity 0.3s ease;
}

.steve-jobs-portrait:hover .portrait-glow {
  opacity: 0.3;
}

.steve-jobs-portrait:hover {
  transform: scale(1.05);
}

.steve-jobs-portrait:hover .portrait-svg {
  filter: drop-shadow(0 4px 16px rgba(0, 0, 0, 0.2));
}

/* 眼镜动画 */
.glasses {
  animation: glassesShine 4s ease-in-out infinite;
}

@keyframes glassesShine {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.8; }
}

/* 苹果动画 */
.apple-in-hand {
  animation: appleFloat 3s ease-in-out infinite;
}

@keyframes appleFloat {
  0%, 100% { transform: translate(130px, 160px) rotate(0deg); }
  50% { transform: translate(130px, 158px) rotate(2deg); }
}

/* 素描线条动画 */
.sketch-lines {
  animation: sketchFade 6s ease-in-out infinite;
}

@keyframes sketchFade {
  0%, 100% { opacity: 0.2; }
  50% { opacity: 0.4; }
}

/* 旋转动画 */
@keyframes rotate {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* 深色主题适配 */
@media (prefers-color-scheme: dark) {
  .portrait-container {
    background: linear-gradient(135deg, 
      rgba(255, 255, 255, 0.05) 0%, 
      rgba(255, 255, 255, 0.02) 100%);
    border-color: rgba(255, 255, 255, 0.1);
  }
  
  .portrait-svg {
    filter: drop-shadow(0 2px 8px rgba(0, 0, 0, 0.3));
  }
  
  .steve-jobs-portrait:hover .portrait-svg {
    filter: drop-shadow(0 4px 16px rgba(0, 0, 0, 0.4));
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .steve-jobs-portrait {
    width: 100px;
    height: 120px;
  }
  
  .portrait-large {
    width: 140px;
    height: 168px;
  }
}
</style>
