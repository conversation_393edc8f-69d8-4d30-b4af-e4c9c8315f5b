<template>
  <div class="steve-jobs-quote" :class="{ 'quote-compact': compact }">
    <!-- 玻璃立体效果容器 -->
    <div class="quote-container">
      <!-- 背景装饰 -->
      <div class="quote-background">
        <div class="glass-layer"></div>
        <div class="gradient-overlay"></div>
      </div>
      
      <!-- 引号装饰 -->
      <div class="quote-marks">
        <div class="quote-mark quote-mark-left">"</div>
        <div class="quote-mark quote-mark-right">"</div>
      </div>
      
      <!-- 主要文字内容 -->
      <div class="quote-content">
        <div class="quote-text-container">
          <h2 class="quote-text-chinese">
            <span class="text-gradient">活着就是为了改变世界</span>
          </h2>
          <p class="quote-text-english">
            <span class="text-gradient-subtle">Stay hungry, stay foolish</span>
          </p>
        </div>
        
        <!-- 作者署名 -->
        <div class="quote-author">
          <div class="author-line"></div>
          <span class="author-name">史蒂夫·乔布斯</span>
          <span class="author-title">Apple 联合创始人</span>
        </div>
      </div>
      
      <!-- Apple Logo 装饰 -->
      <div class="apple-logo-decoration">
        <svg class="apple-logo" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
          <defs>
            <linearGradient id="appleLogoGradient" x1="0%" y1="0%" x2="100%" y2="100%">
              <stop offset="0%" style="stop-color:#ff6b6b"/>
              <stop offset="25%" style="stop-color:#feca57"/>
              <stop offset="50%" style="stop-color:#48dbfb"/>
              <stop offset="75%" style="stop-color:#ff9ff3"/>
              <stop offset="100%" style="stop-color:#54a0ff"/>
            </linearGradient>
          </defs>
          <path d="M18.71 19.5c-.83 1.24-1.71 2.45-3.05 2.47-1.34.03-1.77-.79-3.29-.79-1.53 0-2 .77-3.27.82-1.31.05-2.3-1.32-3.14-2.53C4.25 17 2.94 12.45 4.7 9.39c.87-1.52 2.43-2.48 4.12-2.51 1.28-.02 2.5.87 3.29.87.78 0 2.26-1.07 3.81-.91.65.03 2.47.26 3.64 1.98-.09.06-2.17 1.28-2.15 3.81.03 3.02 2.65 4.03 2.68 4.04-.03.07-.42 1.44-1.38 2.83M13 3.5c.73-.83 1.94-1.46 2.94-1.5.13 1.17-.34 2.35-1.04 3.19-.69.85-1.83 1.51-2.95 1.42-.15-1.15.41-2.35 1.05-3.11z" 
                fill="url(#appleLogoGradient)"/>
        </svg>
      </div>
      
      <!-- 粒子效果 -->
      <div class="particles">
        <div class="particle" v-for="i in 12" :key="i" :style="getParticleStyle(i)"></div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'

interface Props {
  compact?: boolean
}

defineProps<Props>()

// 生成粒子样式
const getParticleStyle = (index: number) => {
  const angle = (index * 30) % 360
  const radius = 80 + (index % 3) * 20
  const delay = index * 0.5
  
  return {
    '--angle': `${angle}deg`,
    '--radius': `${radius}px`,
    '--delay': `${delay}s`,
    '--duration': `${8 + (index % 4)}s`
  }
}
</script>

<style scoped>
.steve-jobs-quote {
  position: relative;
  width: 100%;
  max-width: 600px;
  margin: 0 auto;
  padding: 20px;
}

.quote-compact {
  max-width: 400px;
  padding: 15px;
}

.quote-container {
  position: relative;
  padding: 40px 30px;
  border-radius: 24px;
  overflow: hidden;
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 
    0 20px 40px rgba(0, 0, 0, 0.1),
    0 8px 16px rgba(0, 0, 0, 0.05),
    inset 0 1px 0 rgba(255, 255, 255, 0.3),
    inset 0 -1px 0 rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.quote-container:hover {
  transform: translateY(-5px);
  box-shadow: 
    0 30px 60px rgba(0, 0, 0, 0.15),
    0 12px 24px rgba(0, 0, 0, 0.08),
    inset 0 1px 0 rgba(255, 255, 255, 0.4),
    inset 0 -1px 0 rgba(0, 0, 0, 0.1);
}

.quote-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: -1;
}

.glass-layer {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, 
    rgba(255, 255, 255, 0.15) 0%,
    rgba(255, 255, 255, 0.05) 50%,
    rgba(255, 255, 255, 0.1) 100%);
}

.gradient-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: conic-gradient(
    from 0deg at 50% 50%,
    rgba(255, 107, 107, 0.1) 0deg,
    rgba(254, 202, 87, 0.1) 72deg,
    rgba(72, 219, 251, 0.1) 144deg,
    rgba(255, 159, 243, 0.1) 216deg,
    rgba(84, 160, 255, 0.1) 288deg,
    rgba(255, 107, 107, 0.1) 360deg
  );
  animation: gradientRotate 20s linear infinite;
}

.quote-marks {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
}

.quote-mark {
  position: absolute;
  font-size: 80px;
  font-weight: 300;
  color: rgba(255, 255, 255, 0.1);
  font-family: 'Georgia', serif;
  line-height: 1;
}

.quote-mark-left {
  top: 10px;
  left: 15px;
}

.quote-mark-right {
  bottom: 10px;
  right: 15px;
  transform: rotate(180deg);
}

.quote-content {
  position: relative;
  z-index: 2;
  text-align: center;
}

.quote-text-container {
  margin-bottom: 30px;
}

.quote-text-chinese {
  font-size: 28px;
  font-weight: 600;
  margin: 0 0 15px 0;
  line-height: 1.3;
  letter-spacing: 0.5px;
}

.quote-text-english {
  font-size: 16px;
  font-weight: 400;
  margin: 0;
  font-style: italic;
  opacity: 0.8;
}

.text-gradient {
  background: linear-gradient(135deg, 
    #ff6b6b 0%,
    #feca57 25%,
    #48dbfb 50%,
    #ff9ff3 75%,
    #54a0ff 100%);
  background-size: 200% 200%;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  animation: textGradientShift 8s ease-in-out infinite;
}

.text-gradient-subtle {
  background: linear-gradient(135deg, 
    rgba(255, 255, 255, 0.9) 0%,
    rgba(255, 255, 255, 0.7) 50%,
    rgba(255, 255, 255, 0.9) 100%);
  background-size: 200% 200%;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  animation: textGradientShift 6s ease-in-out infinite reverse;
}

.quote-author {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
  font-size: 14px;
  color: rgba(255, 255, 255, 0.7);
}

.author-line {
  width: 30px;
  height: 1px;
  background: linear-gradient(90deg, 
    transparent 0%, 
    rgba(255, 255, 255, 0.5) 50%, 
    transparent 100%);
}

.author-name {
  font-weight: 600;
  color: rgba(255, 255, 255, 0.9);
}

.author-title {
  font-size: 12px;
  opacity: 0.6;
}

.apple-logo-decoration {
  position: absolute;
  top: 20px;
  right: 20px;
  width: 24px;
  height: 24px;
  opacity: 0.3;
}

.apple-logo {
  width: 100%;
  height: 100%;
  animation: logoFloat 4s ease-in-out infinite;
}

.particles {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  pointer-events: none;
}

.particle {
  position: absolute;
  width: 3px;
  height: 3px;
  background: radial-gradient(circle, 
    rgba(255, 255, 255, 0.8) 0%, 
    rgba(255, 255, 255, 0.2) 70%, 
    transparent 100%);
  border-radius: 50%;
  animation: particleOrbit var(--duration, 8s) linear infinite;
  animation-delay: var(--delay, 0s);
}

/* 动画定义 */
@keyframes gradientRotate {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

@keyframes textGradientShift {
  0%, 100% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
}

@keyframes logoFloat {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  50% { transform: translateY(-3px) rotate(5deg); }
}

@keyframes particleOrbit {
  from {
    transform: rotate(var(--angle, 0deg)) translateX(var(--radius, 80px)) rotate(calc(-1 * var(--angle, 0deg)));
  }
  to {
    transform: rotate(calc(var(--angle, 0deg) + 360deg)) translateX(var(--radius, 80px)) rotate(calc(-1 * (var(--angle, 0deg) + 360deg)));
  }
}

/* 深色主题适配 */
@media (prefers-color-scheme: dark) {
  .glass-layer {
    background: linear-gradient(135deg, 
      rgba(255, 255, 255, 0.08) 0%,
      rgba(255, 255, 255, 0.02) 50%,
      rgba(255, 255, 255, 0.05) 100%);
  }
  
  .quote-container {
    border-color: rgba(255, 255, 255, 0.1);
    box-shadow: 
      0 20px 40px rgba(0, 0, 0, 0.3),
      0 8px 16px rgba(0, 0, 0, 0.2),
      inset 0 1px 0 rgba(255, 255, 255, 0.1),
      inset 0 -1px 0 rgba(0, 0, 0, 0.2);
  }
  
  .quote-container:hover {
    box-shadow: 
      0 30px 60px rgba(0, 0, 0, 0.4),
      0 12px 24px rgba(0, 0, 0, 0.25),
      inset 0 1px 0 rgba(255, 255, 255, 0.15),
      inset 0 -1px 0 rgba(0, 0, 0, 0.2);
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .quote-container {
    padding: 30px 20px;
  }
  
  .quote-text-chinese {
    font-size: 24px;
  }
  
  .quote-text-english {
    font-size: 14px;
  }
  
  .quote-mark {
    font-size: 60px;
  }
  
  .quote-compact .quote-text-chinese {
    font-size: 20px;
  }
  
  .quote-compact .quote-container {
    padding: 25px 15px;
  }
}

@media (max-width: 480px) {
  .quote-text-chinese {
    font-size: 20px;
  }
  
  .quote-text-english {
    font-size: 12px;
  }
  
  .quote-author {
    flex-direction: column;
    gap: 8px;
  }
  
  .author-line {
    width: 50px;
  }
}
</style>
