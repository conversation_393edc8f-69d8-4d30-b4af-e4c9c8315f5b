<template>
  <div class="community-center">
    <!-- 社区统计 -->
    <div class="community-stats">
      <div class="stat-card">
        <div class="stat-icon">
          <AppleIcon name="community" size="large" variant="fill" />
        </div>
        <div class="stat-content">
          <h3>活跃用户</h3>
          <p class="stat-number">{{ activeUsers }}</p>
          <span class="stat-change positive">+{{ newUsers }} 本周新增</span>
        </div>
      </div>
      
      <div class="stat-card">
        <div class="stat-icon">
          <AppleIcon name="chat" size="large" variant="fill" />
        </div>
        <div class="stat-content">
          <h3>讨论话题</h3>
          <p class="stat-number">{{ totalTopics }}</p>
          <span class="stat-change positive">+{{ newTopics }} 今日新增</span>
        </div>
      </div>
      
      <div class="stat-card">
        <div class="stat-icon">
          <AppleIcon name="star" size="large" variant="fill" />
        </div>
        <div class="stat-content">
          <h3>精华内容</h3>
          <p class="stat-number">{{ featuredContent }}</p>
          <span class="stat-change">高质量分享</span>
        </div>
      </div>
    </div>

    <!-- 热门讨论 -->
    <div class="hot-discussions">
      <h2 class="section-title">
        <AppleIcon name="fire" size="large" />
        热门讨论
      </h2>
      <div class="discussions-list">
        <div 
          v-for="discussion in hotDiscussions" 
          :key="discussion.id"
          class="discussion-card"
          @click="openDiscussion(discussion)"
        >
          <div class="discussion-avatar">
            <img :src="discussion.author.avatar" :alt="discussion.author.name" />
            <div class="user-badge" v-if="discussion.author.badge">
              <AppleIcon :name="discussion.author.badge" size="small" />
            </div>
          </div>
          
          <div class="discussion-content">
            <div class="discussion-header">
              <h3 class="discussion-title">{{ discussion.title }}</h3>
              <div class="discussion-tags">
                <span 
                  v-for="tag in discussion.tags.slice(0, 2)" 
                  :key="tag"
                  class="discussion-tag"
                >
                  {{ tag }}
                </span>
              </div>
            </div>
            
            <p class="discussion-preview">{{ discussion.preview }}</p>
            
            <div class="discussion-meta">
              <div class="meta-left">
                <span class="author-name">{{ discussion.author.name }}</span>
                <span class="post-time">{{ discussion.createdAt }}</span>
              </div>
              <div class="meta-right">
                <div class="meta-item">
                  <AppleIcon name="message" size="small" />
                  <span>{{ discussion.replies }}</span>
                </div>
                <div class="meta-item">
                  <AppleIcon name="heart" size="small" />
                  <span>{{ discussion.likes }}</span>
                </div>
                <div class="meta-item">
                  <AppleIcon name="eye" size="small" />
                  <span>{{ formatNumber(discussion.views) }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 用户贡献 -->
    <div class="user-contributions">
      <h2 class="section-title">
        <AppleIcon name="gift" size="large" />
        用户贡献
      </h2>
      <div class="contributions-grid">
        <div 
          v-for="contribution in userContributions" 
          :key="contribution.id"
          class="contribution-card"
          @click="viewContribution(contribution)"
        >
          <div class="contribution-header">
            <div class="contribution-type" :class="contribution.type.toLowerCase()">
              <AppleIcon :name="getContributionIcon(contribution.type)" size="medium" />
              <span>{{ contribution.type }}</span>
            </div>
            <div class="contribution-rating">
              <AppleIcon name="star" size="small" />
              <span>{{ contribution.rating }}</span>
            </div>
          </div>
          
          <div class="contribution-content">
            <h3 class="contribution-title">{{ contribution.title }}</h3>
            <p class="contribution-description">{{ contribution.description }}</p>
            
            <div class="contribution-author">
              <img :src="contribution.author.avatar" :alt="contribution.author.name" />
              <div class="author-info">
                <span class="author-name">{{ contribution.author.name }}</span>
                <span class="author-level">{{ contribution.author.level }}</span>
              </div>
            </div>
            
            <div class="contribution-stats">
              <div class="stat-item">
                <AppleIcon name="download" size="small" />
                <span>{{ contribution.downloads }}</span>
              </div>
              <div class="stat-item">
                <AppleIcon name="heart" size="small" />
                <span>{{ contribution.likes }}</span>
              </div>
              <div class="stat-item">
                <AppleIcon name="message" size="small" />
                <span>{{ contribution.comments }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 问答区域 -->
    <div class="qa-section">
      <div class="section-header">
        <h2 class="section-title">
          <AppleIcon name="help" size="large" />
          问答中心
        </h2>
        <button class="ask-button" @click="askQuestion">
          <AppleIcon name="plus" size="small" />
          提问
        </button>
      </div>
      
      <div class="qa-list">
        <div 
          v-for="qa in qaItems" 
          :key="qa.id"
          class="qa-card"
          @click="openQA(qa)"
        >
          <div class="qa-status" :class="qa.status.toLowerCase()">
            <AppleIcon :name="getStatusIcon(qa.status)" size="small" />
          </div>
          
          <div class="qa-content">
            <h3 class="qa-title">{{ qa.title }}</h3>
            <p class="qa-preview">{{ qa.preview }}</p>
            
            <div class="qa-tags">
              <span 
                v-for="tag in qa.tags.slice(0, 3)" 
                :key="tag"
                class="qa-tag"
              >
                {{ tag }}
              </span>
            </div>
            
            <div class="qa-meta">
              <div class="qa-author">
                <img :src="qa.author.avatar" :alt="qa.author.name" />
                <span>{{ qa.author.name }}</span>
              </div>
              <div class="qa-stats">
                <span class="qa-answers">{{ qa.answers }} 回答</span>
                <span class="qa-time">{{ qa.createdAt }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import AppleIcon from '../AppleIcon.vue'

// 接口定义
interface Discussion {
  id: string
  title: string
  preview: string
  tags: string[]
  author: {
    name: string
    avatar: string
    badge?: string
  }
  createdAt: string
  replies: number
  likes: number
  views: number
}

interface Contribution {
  id: string
  title: string
  description: string
  type: string
  rating: number
  author: {
    name: string
    avatar: string
    level: string
  }
  downloads: number
  likes: number
  comments: number
}

interface QAItem {
  id: string
  title: string
  preview: string
  tags: string[]
  status: string
  author: {
    name: string
    avatar: string
  }
  answers: number
  createdAt: string
}

// 响应式数据
const activeUsers = ref('12.5K')
const newUsers = ref('234')
const totalTopics = ref('3.2K')
const newTopics = ref('45')
const featuredContent = ref('156')

// 热门讨论
const hotDiscussions = ref<Discussion[]>([
  {
    id: '1',
    title: 'Apple Silicon 上的 Docker 性能优化技巧分享',
    preview: '经过几个月的实践，总结了一些在 Apple Silicon 芯片上优化 Docker 性能的实用技巧...',
    tags: ['Docker', 'Apple Silicon', '性能优化'],
    author: {
      name: 'TechGuru',
      avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=40&h=40&fit=crop&crop=face',
      badge: 'star'
    },
    createdAt: '2小时前',
    replies: 23,
    likes: 156,
    views: 2340
  },
  {
    id: '2',
    title: 'Kubernetes 集群监控最佳实践讨论',
    preview: '想和大家讨论一下 Kubernetes 集群监控的最佳实践，特别是在 Apple 生态系统中...',
    tags: ['Kubernetes', '监控', '最佳实践'],
    author: {
      name: 'DevOpsExpert',
      avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=40&h=40&fit=crop&crop=face'
    },
    createdAt: '4小时前',
    replies: 18,
    likes: 89,
    views: 1560
  }
])

// 用户贡献
const userContributions = ref<Contribution[]>([
  {
    id: '1',
    title: 'Apple Silicon Docker 优化脚本',
    description: '一键优化 Docker 在 Apple Silicon 上的性能配置',
    type: '脚本工具',
    rating: 4.8,
    author: {
      name: 'CodeMaster',
      avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=40&h=40&fit=crop&crop=face',
      level: '高级开发者'
    },
    downloads: 1247,
    likes: 234,
    comments: 45
  },
  {
    id: '2',
    title: 'Kubernetes 部署模板集合',
    description: '常用应用的 Kubernetes 部署模板，针对 Apple 环境优化',
    type: '配置模板',
    rating: 4.6,
    author: {
      name: 'K8sNinja',
      avatar: 'https://images.unsplash.com/photo-1535713875002-d1d0cf377fde?w=40&h=40&fit=crop&crop=face',
      level: '专家'
    },
    downloads: 892,
    likes: 167,
    comments: 32
  }
])

// 问答项目
const qaItems = ref<QAItem[]>([
  {
    id: '1',
    title: '如何在 Apple Silicon 上运行 x86 容器？',
    preview: '我需要在 M4 芯片上运行一些只有 x86 版本的容器镜像，有什么好的解决方案吗？',
    tags: ['Apple Silicon', 'x86', '兼容性'],
    status: '已解决',
    author: {
      name: 'NewDeveloper',
      avatar: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=40&h=40&fit=crop&crop=face'
    },
    answers: 5,
    createdAt: '1天前'
  },
  {
    id: '2',
    title: 'Kubernetes 网络策略配置问题',
    preview: '在配置 Kubernetes 网络策略时遇到了一些问题，Pod 之间无法正常通信...',
    tags: ['Kubernetes', '网络策略', '故障排除'],
    status: '待解决',
    author: {
      name: 'CloudEngineer',
      avatar: 'https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=40&h=40&fit=crop&crop=face'
    },
    answers: 2,
    createdAt: '3小时前'
  }
])

// 方法
const openDiscussion = (discussion: Discussion) => {
  console.log('打开讨论:', discussion.title)
}

const viewContribution = (contribution: Contribution) => {
  console.log('查看贡献:', contribution.title)
}

const askQuestion = () => {
  console.log('提问')
}

const openQA = (qa: QAItem) => {
  console.log('打开问答:', qa.title)
}

const getContributionIcon = (type: string): string => {
  switch (type) {
    case '脚本工具':
      return 'toolbox'
    case '配置模板':
      return 'document'
    case '教程文档':
      return 'library'
    default:
      return 'gift'
  }
}

const getStatusIcon = (status: string): string => {
  switch (status) {
    case '已解决':
      return 'check'
    case '待解决':
      return 'clock'
    case '讨论中':
      return 'message'
    default:
      return 'help'
  }
}

const formatNumber = (num: number): string => {
  if (num >= 1000) {
    return (num / 1000).toFixed(1) + 'K'
  }
  return num.toString()
}

// 生命周期
onMounted(() => {
  console.log('社区中心组件已加载')
})
</script>

<style scoped>
.community-center {
  display: flex;
  flex-direction: column;
  gap: 32px;
}

/* 社区统计 */
.community-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
}

.stat-card {
  display: flex;
  align-items: center;
  gap: 16px;
  background: var(--gradient-card);
  backdrop-filter: blur(20px);
  border: 1px solid var(--border-color);
  border-radius: 16px;
  padding: 20px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.stat-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 16px 48px var(--shadow-medium);
}

.stat-icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  background: var(--accent-green);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
}

.stat-content h3 {
  font-size: 14px;
  font-weight: 500;
  color: var(--text-secondary);
  margin: 0 0 4px 0;
}

.stat-number {
  font-size: 24px;
  font-weight: 700;
  color: var(--text-primary);
  margin: 0 0 4px 0;
  font-family: 'SF Mono', Monaco, monospace;
}

.stat-change {
  font-size: 12px;
  color: var(--text-tertiary);
}

.stat-change.positive {
  color: var(--accent-green);
}

/* 通用样式 */
.section-title {
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 24px;
  font-weight: 700;
  color: var(--text-primary);
  margin: 0 0 24px 0;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

/* 热门讨论 */
.discussions-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.discussion-card {
  display: flex;
  gap: 16px;
  background: var(--gradient-card);
  backdrop-filter: blur(20px);
  border: 1px solid var(--border-color);
  border-radius: 16px;
  padding: 20px;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.discussion-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 12px 32px var(--shadow-light);
}

.discussion-avatar {
  position: relative;
  flex-shrink: 0;
}

.discussion-avatar img {
  width: 48px;
  height: 48px;
  border-radius: 12px;
}

.user-badge {
  position: absolute;
  bottom: -2px;
  right: -2px;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background: var(--accent-orange);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  border: 2px solid var(--bg-primary);
}

.discussion-content {
  flex: 1;
}

.discussion-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 12px;
  gap: 16px;
}

.discussion-title {
  font-size: 18px;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0;
  line-height: 1.4;
}

.discussion-tags {
  display: flex;
  gap: 6px;
  flex-shrink: 0;
}

.discussion-tag {
  padding: 4px 8px;
  background: var(--accent-blue);
  color: white;
  border-radius: 6px;
  font-size: 11px;
  font-weight: 500;
}

.discussion-preview {
  font-size: 14px;
  color: var(--text-secondary);
  line-height: 1.5;
  margin: 0 0 16px 0;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.discussion-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.meta-left {
  display: flex;
  gap: 8px;
  align-items: center;
}

.author-name {
  font-size: 14px;
  font-weight: 500;
  color: var(--text-primary);
}

.post-time {
  font-size: 12px;
  color: var(--text-tertiary);
}

.meta-right {
  display: flex;
  gap: 16px;
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  color: var(--text-tertiary);
}

/* 用户贡献 */
.contributions-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 24px;
}

.contribution-card {
  background: var(--gradient-card);
  backdrop-filter: blur(20px);
  border: 1px solid var(--border-color);
  border-radius: 16px;
  padding: 20px;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.contribution-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 16px 48px var(--shadow-medium);
}

.contribution-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.contribution-type {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 6px 12px;
  border-radius: 8px;
  font-size: 12px;
  font-weight: 600;
}

.contribution-type.脚本工具 {
  background: rgba(52, 199, 89, 0.1);
  color: var(--accent-green);
}

.contribution-type.配置模板 {
  background: rgba(0, 122, 255, 0.1);
  color: var(--accent-blue);
}

.contribution-rating {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 14px;
  font-weight: 600;
  color: var(--accent-orange);
}

.contribution-title {
  font-size: 18px;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0 0 8px 0;
}

.contribution-description {
  font-size: 14px;
  color: var(--text-secondary);
  line-height: 1.5;
  margin: 0 0 16px 0;
}

.contribution-author {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 16px;
}

.contribution-author img {
  width: 32px;
  height: 32px;
  border-radius: 8px;
}

.author-info {
  display: flex;
  flex-direction: column;
}

.author-name {
  font-size: 14px;
  font-weight: 500;
  color: var(--text-primary);
}

.author-level {
  font-size: 12px;
  color: var(--text-tertiary);
}

.contribution-stats {
  display: flex;
  gap: 16px;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  color: var(--text-tertiary);
}

/* 问答区域 */
.ask-button {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 20px;
  background: var(--accent-blue);
  color: white;
  border: none;
  border-radius: 12px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
}

.ask-button:hover {
  background: var(--accent-blue);
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(0, 122, 255, 0.3);
}

.qa-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.qa-card {
  display: flex;
  gap: 16px;
  background: var(--gradient-card);
  backdrop-filter: blur(20px);
  border: 1px solid var(--border-color);
  border-radius: 16px;
  padding: 20px;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.qa-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 12px 32px var(--shadow-light);
}

.qa-status {
  width: 40px;
  height: 40px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.qa-status.已解决 {
  background: rgba(52, 199, 89, 0.1);
  color: var(--accent-green);
}

.qa-status.待解决 {
  background: rgba(255, 149, 0, 0.1);
  color: var(--accent-orange);
}

.qa-content {
  flex: 1;
}

.qa-title {
  font-size: 16px;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0 0 8px 0;
}

.qa-preview {
  font-size: 14px;
  color: var(--text-secondary);
  line-height: 1.5;
  margin: 0 0 12px 0;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.qa-tags {
  display: flex;
  gap: 6px;
  margin-bottom: 12px;
  flex-wrap: wrap;
}

.qa-tag {
  padding: 4px 8px;
  background: var(--bg-tertiary);
  color: var(--text-secondary);
  border-radius: 6px;
  font-size: 11px;
  font-weight: 500;
}

.qa-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.qa-author {
  display: flex;
  align-items: center;
  gap: 8px;
}

.qa-author img {
  width: 24px;
  height: 24px;
  border-radius: 50%;
}

.qa-author span {
  font-size: 12px;
  color: var(--text-secondary);
  font-weight: 500;
}

.qa-stats {
  display: flex;
  gap: 12px;
  font-size: 12px;
  color: var(--text-tertiary);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .contributions-grid {
    grid-template-columns: 1fr;
  }
  
  .discussion-header {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .discussion-tags {
    margin-top: 8px;
  }
  
  .meta-right {
    gap: 12px;
  }
}
</style>
