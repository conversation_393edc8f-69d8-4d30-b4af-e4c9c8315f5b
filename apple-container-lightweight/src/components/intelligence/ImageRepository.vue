<template>
  <div class="image-repository">
    <!-- 统计概览 -->
    <div class="stats-overview">
      <div class="stat-card">
        <div class="stat-icon">
          <AppleIcon name="containers" size="large" variant="fill" />
        </div>
        <div class="stat-content">
          <h3>总镜像数</h3>
          <p class="stat-number">{{ totalImages }}</p>
          <span class="stat-change positive">+{{ newImages }} 本周新增</span>
        </div>
      </div>
      
      <div class="stat-card">
        <div class="stat-icon">
          <AppleIcon name="download" size="large" variant="fill" />
        </div>
        <div class="stat-content">
          <h3>下载量</h3>
          <p class="stat-number">{{ totalDownloads }}</p>
          <span class="stat-change positive">+{{ weeklyDownloads }} 本周</span>
        </div>
      </div>
      
      <div class="stat-card">
        <div class="stat-icon">
          <AppleIcon name="star" size="large" variant="fill" />
        </div>
        <div class="stat-content">
          <h3>热门镜像</h3>
          <p class="stat-number">{{ popularImages }}</p>
          <span class="stat-change">高评分镜像</span>
        </div>
      </div>
    </div>

    <!-- 分类导航 -->
    <div class="category-nav">
      <button 
        v-for="category in categories" 
        :key="category.id"
        class="category-button"
        :class="{ active: selectedCategory === category.id }"
        @click="selectCategory(category.id)"
      >
        <AppleIcon :name="category.icon" size="medium" />
        <span>{{ category.name }}</span>
        <span class="category-count">{{ category.count }}</span>
      </button>
    </div>

    <!-- 镜像网格 -->
    <div class="images-grid">
      <div 
        v-for="image in filteredImages" 
        :key="image.id"
        class="image-card"
        @click="selectImage(image)"
      >
        <div class="image-header">
          <div class="image-logo">
            <img v-if="image.logo" :src="image.logo" :alt="image.name" />
            <AppleIcon v-else :name="image.icon || 'containers'" size="large" />
          </div>
          <div class="image-actions">
            <button class="action-button" @click.stop="toggleFavorite(image.id)">
              <AppleIcon 
                :name="image.isFavorite ? 'heart' : 'heart'" 
                size="small" 
                :variant="image.isFavorite ? 'fill' : 'regular'"
                :class="{ favorite: image.isFavorite }"
              />
            </button>
            <button class="action-button" @click.stop="shareImage(image)">
              <AppleIcon name="share" size="small" />
            </button>
          </div>
        </div>
        
        <div class="image-content">
          <h3 class="image-name">{{ image.name }}</h3>
          <p class="image-description">{{ image.description }}</p>
          
          <div class="image-tags">
            <span 
              v-for="tag in image.tags.slice(0, 3)" 
              :key="tag"
              class="image-tag"
            >
              {{ tag }}
            </span>
            <span v-if="image.tags.length > 3" class="more-tags">
              +{{ image.tags.length - 3 }}
            </span>
          </div>
          
          <div class="image-meta">
            <div class="meta-item">
              <AppleIcon name="download" size="small" />
              <span>{{ formatNumber(image.downloads) }}</span>
            </div>
            <div class="meta-item">
              <AppleIcon name="star" size="small" />
              <span>{{ image.rating }}</span>
            </div>
            <div class="meta-item">
              <span class="architecture-badge" :class="image.architecture.toLowerCase()">
                {{ image.architecture }}
              </span>
            </div>
          </div>
        </div>
        
        <div class="image-footer">
          <div class="version-info">
            <span class="version">v{{ image.version }}</span>
            <span class="updated">{{ image.lastUpdated }}</span>
          </div>
          <button class="deploy-button" @click.stop="deployImage(image)">
            <AppleIcon name="play" size="small" />
            部署
          </button>
        </div>
      </div>
    </div>

    <!-- 加载更多 -->
    <div v-if="hasMore" class="load-more">
      <button class="load-more-button" @click="loadMore" :disabled="loading">
        <AppleIcon v-if="loading" name="refresh" size="medium" class="spinning" />
        <span>{{ loading ? '加载中...' : '加载更多' }}</span>
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import AppleIcon from '../AppleIcon.vue'

// 接口定义
interface ImageItem {
  id: string
  name: string
  description: string
  logo?: string
  icon?: string
  tags: string[]
  downloads: number
  rating: number
  architecture: string
  version: string
  lastUpdated: string
  category: string
  isFavorite: boolean
}

// 响应式数据
const selectedCategory = ref('all')
const loading = ref(false)
const hasMore = ref(true)

// 统计数据
const totalImages = ref(1247)
const newImages = ref(23)
const totalDownloads = ref('2.4M')
const weeklyDownloads = ref('156K')
const popularImages = ref(89)

// 分类配置
const categories = ref([
  { id: 'all', name: '全部', icon: 'library', count: 1247 },
  { id: 'ai-ml', name: 'AI/ML', icon: 'brain', count: 234 },
  { id: 'databases', name: '数据库', icon: 'database', count: 156 },
  { id: 'web-servers', name: 'Web服务', icon: 'server', count: 189 },
  { id: 'dev-tools', name: '开发工具', icon: 'toolbox', count: 298 },
  { id: 'monitoring', name: '监控', icon: 'monitoring', count: 87 },
  { id: 'security', name: '安全', icon: 'shield', count: 123 }
])

// 镜像数据
const images = ref<ImageItem[]>([
  {
    id: '1',
    name: 'TensorFlow',
    description: '开源机器学习框架，支持深度学习和神经网络训练',
    icon: 'brain',
    tags: ['AI', 'ML', 'Python', 'GPU'],
    downloads: 2400000,
    rating: 4.8,
    architecture: 'ARM64',
    version: '2.15.0',
    lastUpdated: '2天前',
    category: 'ai-ml',
    isFavorite: true
  },
  {
    id: '2',
    name: 'PostgreSQL',
    description: '强大的开源关系型数据库系统',
    icon: 'database',
    tags: ['Database', 'SQL', 'ACID'],
    downloads: 1800000,
    rating: 4.9,
    architecture: 'Universal',
    version: '16.1',
    lastUpdated: '1周前',
    category: 'databases',
    isFavorite: false
  },
  {
    id: '3',
    name: 'Nginx',
    description: '高性能的HTTP和反向代理服务器',
    icon: 'server',
    tags: ['Web Server', 'Proxy', 'Load Balancer'],
    downloads: 3200000,
    rating: 4.7,
    architecture: 'ARM64',
    version: '1.25.3',
    lastUpdated: '3天前',
    category: 'web-servers',
    isFavorite: true
  },
  {
    id: '4',
    name: 'Prometheus',
    description: '开源监控和告警工具包',
    icon: 'monitoring',
    tags: ['Monitoring', 'Metrics', 'Alerting'],
    downloads: 890000,
    rating: 4.6,
    architecture: 'ARM64',
    version: '2.48.0',
    lastUpdated: '5天前',
    category: 'monitoring',
    isFavorite: false
  },
  {
    id: '5',
    name: 'Redis',
    description: '内存数据结构存储，用作数据库、缓存和消息代理',
    icon: 'database',
    tags: ['Cache', 'NoSQL', 'In-Memory'],
    downloads: 2100000,
    rating: 4.8,
    architecture: 'Universal',
    version: '7.2.3',
    lastUpdated: '1天前',
    category: 'databases',
    isFavorite: true
  },
  {
    id: '6',
    name: 'Docker Registry',
    description: '私有Docker镜像仓库服务',
    icon: 'containers',
    tags: ['Registry', 'Storage', 'Private'],
    downloads: 1200000,
    rating: 4.5,
    architecture: 'ARM64',
    version: '2.8.3',
    lastUpdated: '1周前',
    category: 'dev-tools',
    isFavorite: false
  }
])

// 计算属性
const filteredImages = computed(() => {
  if (selectedCategory.value === 'all') {
    return images.value
  }
  return images.value.filter(image => image.category === selectedCategory.value)
})

// 方法
const selectCategory = (categoryId: string) => {
  selectedCategory.value = categoryId
}

const selectImage = (image: ImageItem) => {
  console.log('选择镜像:', image.name)
  // 这里可以打开详情面板或跳转到详情页
}

const toggleFavorite = (imageId: string) => {
  const image = images.value.find(img => img.id === imageId)
  if (image) {
    image.isFavorite = !image.isFavorite
  }
}

const shareImage = (image: ImageItem) => {
  console.log('分享镜像:', image.name)
  // 实现分享功能
}

const deployImage = (image: ImageItem) => {
  console.log('部署镜像:', image.name)
  // 实现一键部署功能
}

const loadMore = () => {
  loading.value = true
  // 模拟加载更多数据
  setTimeout(() => {
    loading.value = false
    // 这里可以添加更多镜像数据
  }, 1000)
}

const formatNumber = (num: number): string => {
  if (num >= 1000000) {
    return (num / 1000000).toFixed(1) + 'M'
  } else if (num >= 1000) {
    return (num / 1000).toFixed(1) + 'K'
  }
  return num.toString()
}

// 生命周期
onMounted(() => {
  console.log('镜像仓库组件已加载')
})
</script>

<style scoped>
.image-repository {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

/* 统计概览 */
.stats-overview {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
}

.stat-card {
  display: flex;
  align-items: center;
  gap: 16px;
  background: var(--gradient-card);
  backdrop-filter: blur(20px);
  border: 1px solid var(--border-color);
  border-radius: 16px;
  padding: 20px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.stat-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 16px 48px var(--shadow-medium);
}

.stat-icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  background: var(--accent-blue);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
}

.stat-content h3 {
  font-size: 14px;
  font-weight: 500;
  color: var(--text-secondary);
  margin: 0 0 4px 0;
}

.stat-number {
  font-size: 24px;
  font-weight: 700;
  color: var(--text-primary);
  margin: 0 0 4px 0;
  font-family: 'SF Mono', Monaco, monospace;
}

.stat-change {
  font-size: 12px;
  color: var(--text-tertiary);
}

.stat-change.positive {
  color: var(--accent-green);
}

/* 分类导航 */
.category-nav {
  display: flex;
  gap: 12px;
  overflow-x: auto;
  padding-bottom: 8px;
}

.category-button {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 16px;
  background: var(--gradient-card);
  backdrop-filter: blur(20px);
  border: 1px solid var(--border-color);
  border-radius: 12px;
  color: var(--text-secondary);
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  white-space: nowrap;
}

.category-button:hover {
  color: var(--text-primary);
  transform: translateY(-2px);
  box-shadow: 0 8px 24px var(--shadow-light);
}

.category-button.active {
  background: var(--accent-blue);
  color: white;
  border-color: var(--accent-blue);
}

.category-count {
  background: rgba(255, 255, 255, 0.2);
  padding: 2px 6px;
  border-radius: 8px;
  font-size: 11px;
  font-weight: 600;
}

/* 镜像网格 */
.images-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
  gap: 20px;
}

.image-card {
  background: var(--gradient-card);
  backdrop-filter: blur(20px);
  border: 1px solid var(--border-color);
  border-radius: 16px;
  padding: 20px;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.image-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 16px 48px var(--shadow-medium);
}

.image-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.image-logo {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  background: var(--bg-tertiary);
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
}

.image-logo img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.image-actions {
  display: flex;
  gap: 8px;
}

.action-button {
  width: 32px;
  height: 32px;
  border-radius: 8px;
  background: var(--bg-tertiary);
  border: 1px solid var(--border-color);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
}

.action-button:hover {
  background: var(--bg-secondary);
  transform: scale(1.1);
}

.favorite {
  color: var(--accent-red);
}

.image-name {
  font-size: 18px;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0 0 8px 0;
}

.image-description {
  font-size: 14px;
  color: var(--text-secondary);
  margin: 0 0 16px 0;
  line-height: 1.5;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.image-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
  margin-bottom: 16px;
}

.image-tag {
  padding: 4px 8px;
  background: var(--accent-blue);
  color: white;
  border-radius: 6px;
  font-size: 11px;
  font-weight: 500;
}

.more-tags {
  padding: 4px 8px;
  background: var(--bg-tertiary);
  color: var(--text-tertiary);
  border-radius: 6px;
  font-size: 11px;
  font-weight: 500;
}

.image-meta {
  display: flex;
  gap: 16px;
  margin-bottom: 16px;
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  color: var(--text-tertiary);
}

.architecture-badge {
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 10px;
  font-weight: 600;
}

.architecture-badge.arm64 {
  background: var(--accent-green);
  color: white;
}

.architecture-badge.universal {
  background: var(--accent-blue);
  color: white;
}

.image-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.version-info {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.version {
  font-size: 12px;
  font-weight: 600;
  color: var(--text-primary);
  font-family: 'SF Mono', Monaco, monospace;
}

.updated {
  font-size: 11px;
  color: var(--text-tertiary);
}

.deploy-button {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 16px;
  background: var(--accent-blue);
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 12px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
}

.deploy-button:hover {
  background: var(--accent-blue);
  transform: scale(1.05);
  box-shadow: 0 4px 12px rgba(0, 122, 255, 0.3);
}

/* 加载更多 */
.load-more {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}

.load-more-button {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 24px;
  background: var(--gradient-card);
  backdrop-filter: blur(20px);
  border: 1px solid var(--border-color);
  border-radius: 12px;
  color: var(--text-primary);
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.load-more-button:hover:not(:disabled) {
  background: var(--bg-tertiary);
  transform: translateY(-2px);
  box-shadow: 0 8px 24px var(--shadow-light);
}

.load-more-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.spinning {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .images-grid {
    grid-template-columns: 1fr;
  }
  
  .category-nav {
    flex-wrap: wrap;
  }
}
</style>
