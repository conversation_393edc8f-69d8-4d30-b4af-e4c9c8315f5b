<template>
  <div class="enhanced-image-card" :class="{ 'card-featured': image.featured }">
    <!-- 卡片背景和装饰 -->
    <div class="card-background">
      <div class="glass-layer"></div>
      <div class="gradient-overlay" v-if="image.featured"></div>
    </div>
    
    <!-- 卡片头部 -->
    <div class="card-header">
      <!-- 镜像图标/Logo -->
      <div class="image-icon">
        <img 
          v-if="image.logo" 
          :src="image.logo" 
          :alt="image.name"
          class="icon-image"
        />
        <div v-else class="icon-placeholder">
          <AppleIcon name="cube" size="large" />
        </div>
        
        <!-- 状态指示器 -->
        <div class="status-indicator" :class="`status-${image.status || 'available'}`">
          <div class="status-dot"></div>
        </div>
      </div>
      
      <!-- 基本信息 -->
      <div class="image-info">
        <h3 class="image-name">{{ image.name }}</h3>
        <p class="image-description">{{ image.description }}</p>
        <div class="image-meta">
          <span class="version">v{{ image.version }}</span>
          <span class="architecture">{{ image.architecture }}</span>
          <span class="last-updated">{{ image.lastUpdated }}</span>
        </div>
      </div>
      
      <!-- 操作按钮 -->
      <div class="card-actions">
        <button class="action-btn action-btn-primary" @click="deployImage">
          <AppleIcon name="play.fill" size="small" />
          <span>Deploy</span>
        </button>
        
        <button class="action-btn action-btn-secondary" @click="toggleFavorite">
          <AppleIcon 
            :name="isFavorited ? 'heart.fill' : 'heart'" 
            size="small"
            :class="{ 'favorited': isFavorited }"
          />
        </button>
        
        <button class="action-btn action-btn-secondary" @click="showDetails">
          <AppleIcon name="info.circle" size="small" />
        </button>
      </div>
    </div>
    
    <!-- 资源使用进度条 -->
    <div class="resource-metrics">
      <div class="metric-item">
        <div class="metric-header">
          <AppleIcon name="cpu" size="small" />
          <span class="metric-label">CPU</span>
          <span class="metric-value">{{ image.metrics?.cpu || 0 }}%</span>
        </div>
        <div class="progress-bar">
          <div 
            class="progress-fill cpu-progress" 
            :style="{ width: `${image.metrics?.cpu || 0}%` }"
          ></div>
        </div>
      </div>
      
      <div class="metric-item">
        <div class="metric-header">
          <AppleIcon name="memorychip" size="small" />
          <span class="metric-label">Memory</span>
          <span class="metric-value">{{ image.metrics?.memory || 0 }}%</span>
        </div>
        <div class="progress-bar">
          <div 
            class="progress-fill memory-progress" 
            :style="{ width: `${image.metrics?.memory || 0}%` }"
          ></div>
        </div>
      </div>
      
      <div class="metric-item">
        <div class="metric-header">
          <AppleIcon name="internaldrive" size="small" />
          <span class="metric-label">Storage</span>
          <span class="metric-value">{{ formatBytes(image.metrics?.storage || 0) }}</span>
        </div>
        <div class="progress-bar">
          <div 
            class="progress-fill storage-progress" 
            :style="{ width: `${(image.metrics?.storage || 0) / 1000 * 100}%` }"
          ></div>
        </div>
      </div>
    </div>
    
    <!-- 统计信息 -->
    <div class="card-stats">
      <div class="stat-item">
        <AppleIcon name="arrow.down.circle" size="small" />
        <span class="stat-value">{{ formatNumber(image.downloads) }}</span>
        <span class="stat-label">Downloads</span>
      </div>
      
      <div class="stat-item">
        <div class="rating-stars">
          <AppleIcon 
            v-for="i in 5" 
            :key="i"
            name="star.fill" 
            size="small"
            :class="{ 'star-active': i <= Math.floor(image.rating) }"
          />
        </div>
        <span class="stat-value">{{ image.rating.toFixed(1) }}</span>
      </div>
      
      <div class="stat-item">
        <AppleIcon name="person.2" size="small" />
        <span class="stat-value">{{ formatNumber(image.users || 0) }}</span>
        <span class="stat-label">Users</span>
      </div>
    </div>
    
    <!-- 标签 -->
    <div class="image-tags">
      <span 
        v-for="tag in image.tags.slice(0, 3)" 
        :key="tag" 
        class="tag"
      >
        {{ tag }}
      </span>
      <span v-if="image.tags.length > 3" class="tag tag-more">
        +{{ image.tags.length - 3 }}
      </span>
    </div>
    
    <!-- 悬停效果 -->
    <div class="card-hover-effect"></div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import AppleIcon from '../AppleIcon.vue'

interface ImageMetrics {
  cpu: number
  memory: number
  storage: number
  network: number
}

interface ImageData {
  id: string
  name: string
  description: string
  version: string
  architecture: string
  lastUpdated: string
  downloads: number
  rating: number
  users?: number
  tags: string[]
  logo?: string
  status?: 'running' | 'stopped' | 'updating' | 'available'
  featured?: boolean
  metrics?: ImageMetrics
}

interface Props {
  image: ImageData
}

const props = defineProps<Props>()

// 响应式状态
const isFavorited = ref(false)

// 方法
const deployImage = () => {
  console.log('Deploying image:', props.image.name)
  // 实现部署逻辑
}

const toggleFavorite = () => {
  isFavorited.value = !isFavorited.value
  console.log('Toggle favorite:', props.image.name, isFavorited.value)
}

const showDetails = () => {
  console.log('Show details for:', props.image.name)
  // 实现详情显示逻辑
}

const formatNumber = (num: number): string => {
  if (num >= 1000000) {
    return (num / 1000000).toFixed(1) + 'M'
  } else if (num >= 1000) {
    return (num / 1000).toFixed(1) + 'K'
  }
  return num.toString()
}

const formatBytes = (bytes: number): string => {
  if (bytes >= 1024 * 1024 * 1024) {
    return (bytes / (1024 * 1024 * 1024)).toFixed(1) + 'GB'
  } else if (bytes >= 1024 * 1024) {
    return (bytes / (1024 * 1024)).toFixed(1) + 'MB'
  } else if (bytes >= 1024) {
    return (bytes / 1024).toFixed(1) + 'KB'
  }
  return bytes + 'B'
}
</script>

<style scoped>
.enhanced-image-card {
  position: relative;
  background: var(--gradient-card);
  backdrop-filter: blur(20px);
  border: 1px solid var(--border-color);
  border-radius: 20px;
  padding: 24px;
  transition: all 0.3s ease;
  overflow: hidden;
  cursor: pointer;
}

.enhanced-image-card:hover {
  transform: translateY(-8px);
  box-shadow: 
    0 20px 40px rgba(0, 0, 0, 0.15),
    0 8px 16px rgba(0, 0, 0, 0.1);
  border-color: var(--accent-blue);
}

.card-featured {
  border: 2px solid transparent;
  background: linear-gradient(var(--gradient-card), var(--gradient-card)) padding-box,
              linear-gradient(135deg, #ff6b6b, #feca57, #48dbfb, #ff9ff3, #54a0ff) border-box;
}

.card-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: -1;
}

.glass-layer {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, 
    rgba(255, 255, 255, 0.1) 0%,
    rgba(255, 255, 255, 0.05) 100%);
}

.gradient-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: conic-gradient(
    from 0deg,
    rgba(255, 107, 107, 0.05) 0deg,
    rgba(254, 202, 87, 0.05) 72deg,
    rgba(72, 219, 251, 0.05) 144deg,
    rgba(255, 159, 243, 0.05) 216deg,
    rgba(84, 160, 255, 0.05) 288deg,
    rgba(255, 107, 107, 0.05) 360deg
  );
  animation: gradientRotate 20s linear infinite;
}

.card-header {
  display: flex;
  align-items: flex-start;
  gap: 16px;
  margin-bottom: 20px;
}

.image-icon {
  position: relative;
  width: 56px;
  height: 56px;
  border-radius: 12px;
  overflow: hidden;
  background: rgba(255, 255, 255, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.icon-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.icon-placeholder {
  color: rgba(255, 255, 255, 0.6);
}

.status-indicator {
  position: absolute;
  top: -2px;
  right: -2px;
  width: 16px;
  height: 16px;
  border-radius: 50%;
  border: 2px solid var(--bg-primary);
  display: flex;
  align-items: center;
  justify-content: center;
}

.status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
}

.status-running .status-dot { background: #2ed573; }
.status-stopped .status-dot { background: #ff4757; }
.status-updating .status-dot { background: #ffa502; }
.status-available .status-dot { background: #747d8c; }

.image-info {
  flex: 1;
  min-width: 0;
}

.image-name {
  font-size: 18px;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0 0 8px 0;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.image-description {
  font-size: 14px;
  color: var(--text-secondary);
  margin: 0 0 12px 0;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.image-meta {
  display: flex;
  gap: 12px;
  font-size: 12px;
  color: var(--text-tertiary);
}

.card-actions {
  display: flex;
  gap: 8px;
  flex-shrink: 0;
}

.action-btn {
  background: none;
  border: none;
  cursor: pointer;
  border-radius: 8px;
  padding: 8px;
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 12px;
  font-weight: 500;
  transition: all 0.2s ease;
}

.action-btn-primary {
  background: var(--accent-blue);
  color: white;
  padding: 8px 12px;
}

.action-btn-primary:hover {
  background: var(--accent-blue-hover);
  transform: scale(1.05);
}

.action-btn-secondary {
  background: rgba(255, 255, 255, 0.1);
  color: var(--text-secondary);
}

.action-btn-secondary:hover {
  background: rgba(255, 255, 255, 0.2);
  color: var(--text-primary);
}

.favorited {
  color: #ff4757 !important;
}

.resource-metrics {
  margin-bottom: 20px;
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.metric-item {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.metric-header {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 12px;
}

.metric-label {
  color: var(--text-secondary);
  flex: 1;
}

.metric-value {
  color: var(--text-primary);
  font-weight: 500;
}

.progress-bar {
  height: 4px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 2px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  border-radius: 2px;
  transition: width 0.3s ease;
}

.cpu-progress { background: linear-gradient(90deg, #ff6b6b, #ff4757); }
.memory-progress { background: linear-gradient(90deg, #feca57, #ff9f43); }
.storage-progress { background: linear-gradient(90deg, #48dbfb, #0abde3); }

.card-stats {
  display: flex;
  justify-content: space-between;
  margin-bottom: 16px;
  padding: 12px 0;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
  font-size: 12px;
}

.stat-value {
  font-weight: 600;
  color: var(--text-primary);
}

.stat-label {
  color: var(--text-tertiary);
}

.rating-stars {
  display: flex;
  gap: 2px;
}

.star-active {
  color: #feca57;
}

.image-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.tag {
  background: rgba(255, 255, 255, 0.1);
  color: var(--text-secondary);
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 11px;
  font-weight: 500;
}

.tag-more {
  background: rgba(255, 255, 255, 0.05);
  color: var(--text-tertiary);
}

.card-hover-effect {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, 
    rgba(255, 255, 255, 0.05) 0%,
    rgba(255, 255, 255, 0.02) 100%);
  opacity: 0;
  transition: opacity 0.3s ease;
  pointer-events: none;
}

.enhanced-image-card:hover .card-hover-effect {
  opacity: 1;
}

@keyframes gradientRotate {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .enhanced-image-card {
    padding: 16px;
  }
  
  .card-header {
    flex-direction: column;
    gap: 12px;
  }
  
  .image-icon {
    align-self: center;
  }
  
  .card-actions {
    align-self: center;
  }
  
  .card-stats {
    flex-direction: column;
    gap: 12px;
  }
}
</style>
