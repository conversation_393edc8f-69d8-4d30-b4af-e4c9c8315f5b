<template>
  <div class="toolbox-center">
    <!-- 工具分类 -->
    <div class="tool-categories">
      <h2 class="section-title">
        <AppleIcon name="toolbox" size="large" />
        开发者工具箱
      </h2>
      <div class="categories-grid">
        <div 
          v-for="category in toolCategories" 
          :key="category.id"
          class="category-card"
          :class="{ active: selectedCategory === category.id }"
          @click="selectCategory(category.id)"
        >
          <div class="category-icon">
            <AppleIcon :name="category.icon" size="large" variant="fill" />
          </div>
          <div class="category-content">
            <h3 class="category-name">{{ category.name }}</h3>
            <p class="category-description">{{ category.description }}</p>
            <span class="category-count">{{ category.count }} 个工具</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 推荐工具 -->
    <div class="featured-tools">
      <h2 class="section-title">
        <AppleIcon name="star" size="large" />
        推荐工具
      </h2>
      <div class="featured-grid">
        <div 
          v-for="tool in featuredTools" 
          :key="tool.id"
          class="featured-tool"
          @click="openTool(tool)"
        >
          <div class="tool-preview">
            <img :src="tool.preview" :alt="tool.name" />
            <div class="tool-overlay">
              <button class="launch-button">
                <AppleIcon name="play" size="medium" />
                启动工具
              </button>
            </div>
          </div>
          <div class="tool-info">
            <div class="tool-header">
              <h3 class="tool-name">{{ tool.name }}</h3>
              <div class="tool-rating">
                <AppleIcon name="star" size="small" />
                <span>{{ tool.rating }}</span>
              </div>
            </div>
            <p class="tool-description">{{ tool.description }}</p>
            <div class="tool-tags">
              <span 
                v-for="tag in tool.tags.slice(0, 3)" 
                :key="tag"
                class="tool-tag"
              >
                {{ tag }}
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 工具列表 -->
    <div class="tools-section">
      <div class="section-header">
        <h2 class="section-title">
          <AppleIcon name="grid" size="large" />
          {{ getCategoryName(selectedCategory) }}
        </h2>
        <div class="view-controls">
          <div class="sort-dropdown">
            <select v-model="sortBy" class="sort-select">
              <option value="popular">最受欢迎</option>
              <option value="newest">最新发布</option>
              <option value="rating">评分最高</option>
              <option value="name">名称排序</option>
            </select>
          </div>
        </div>
      </div>

      <div class="tools-grid">
        <div 
          v-for="tool in filteredTools" 
          :key="tool.id"
          class="tool-card"
          @click="openTool(tool)"
        >
          <div class="tool-icon">
            <AppleIcon :name="tool.icon" size="large" />
          </div>
          
          <div class="tool-content">
            <div class="tool-header">
              <h3 class="tool-name">{{ tool.name }}</h3>
              <div class="tool-status" :class="tool.status.toLowerCase()">
                {{ tool.status }}
              </div>
            </div>
            
            <p class="tool-description">{{ tool.description }}</p>
            
            <div class="tool-features">
              <ul>
                <li v-for="feature in tool.features.slice(0, 3)" :key="feature">
                  {{ feature }}
                </li>
              </ul>
            </div>
            
            <div class="tool-footer">
              <div class="tool-meta">
                <div class="meta-item">
                  <AppleIcon name="users" size="small" />
                  <span>{{ formatNumber(tool.users) }}</span>
                </div>
                <div class="meta-item">
                  <AppleIcon name="star" size="small" />
                  <span>{{ tool.rating }}</span>
                </div>
                <div class="meta-item">
                  <AppleIcon name="clock" size="small" />
                  <span>{{ tool.lastUpdated }}</span>
                </div>
              </div>
              
              <div class="tool-actions">
                <button class="action-button secondary" @click.stop="bookmarkTool(tool.id)">
                  <AppleIcon 
                    name="bookmark" 
                    size="small" 
                    :variant="tool.isBookmarked ? 'fill' : 'regular'"
                  />
                </button>
                <button class="action-button primary" @click.stop="launchTool(tool)">
                  <AppleIcon name="external-link" size="small" />
                  启动
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 自定义工具 -->
    <div class="custom-tools">
      <div class="section-header">
        <h2 class="section-title">
          <AppleIcon name="plus" size="large" />
          自定义工具
        </h2>
        <button class="add-tool-button" @click="addCustomTool">
          <AppleIcon name="plus" size="small" />
          添加工具
        </button>
      </div>
      
      <div class="custom-tools-grid">
        <div 
          v-for="customTool in customTools" 
          :key="customTool.id"
          class="custom-tool-card"
        >
          <div class="custom-tool-header">
            <div class="custom-tool-icon">
              <AppleIcon :name="customTool.icon" size="medium" />
            </div>
            <div class="custom-tool-actions">
              <button class="tool-action" @click="editCustomTool(customTool)">
                <AppleIcon name="edit" size="small" />
              </button>
              <button class="tool-action" @click="deleteCustomTool(customTool.id)">
                <AppleIcon name="trash" size="small" />
              </button>
            </div>
          </div>
          
          <div class="custom-tool-content">
            <h3 class="custom-tool-name">{{ customTool.name }}</h3>
            <p class="custom-tool-description">{{ customTool.description }}</p>
            <div class="custom-tool-url">
              <AppleIcon name="link" size="small" />
              <span>{{ customTool.url }}</span>
            </div>
          </div>
          
          <button class="launch-custom-tool" @click="launchCustomTool(customTool)">
            <AppleIcon name="external-link" size="small" />
            启动工具
          </button>
        </div>
        
        <!-- 添加工具卡片 -->
        <div class="add-tool-card" @click="addCustomTool">
          <div class="add-tool-icon">
            <AppleIcon name="plus" size="xlarge" />
          </div>
          <p class="add-tool-text">添加自定义工具</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import AppleIcon from '../AppleIcon.vue'

// 接口定义
interface ToolCategory {
  id: string
  name: string
  description: string
  icon: string
  count: number
}

interface Tool {
  id: string
  name: string
  description: string
  icon: string
  category: string
  status: string
  rating: number
  users: number
  lastUpdated: string
  features: string[]
  tags: string[]
  url: string
  isBookmarked: boolean
  preview?: string
}

interface CustomTool {
  id: string
  name: string
  description: string
  icon: string
  url: string
}

// 响应式数据
const selectedCategory = ref('all')
const sortBy = ref('popular')

// 工具分类
const toolCategories = ref<ToolCategory[]>([
  {
    id: 'all',
    name: '全部工具',
    description: '浏览所有可用的开发工具',
    icon: 'grid',
    count: 67
  },
  {
    id: 'development',
    name: '开发工具',
    description: '代码编辑、调试和构建工具',
    icon: 'toolbox',
    count: 23
  },
  {
    id: 'monitoring',
    name: '监控工具',
    description: '性能监控和日志分析工具',
    icon: 'monitoring',
    count: 15
  },
  {
    id: 'security',
    name: '安全工具',
    description: '安全扫描和漏洞检测工具',
    icon: 'shield',
    count: 12
  },
  {
    id: 'utilities',
    name: '实用工具',
    description: '各种实用的辅助工具',
    icon: 'settings',
    count: 17
  }
])

// 推荐工具
const featuredTools = ref<Tool[]>([
  {
    id: 'featured-1',
    name: 'Container Inspector',
    description: '强大的容器检查和调试工具，支持实时监控',
    icon: 'search',
    category: 'development',
    status: '在线',
    rating: 4.9,
    users: 12500,
    lastUpdated: '2天前',
    features: ['实时监控', '性能分析', '日志查看'],
    tags: ['Docker', '调试', '监控'],
    url: 'https://container-inspector.dev',
    isBookmarked: false,
    preview: 'https://images.unsplash.com/photo-1551288049-bebda4e38f71?w=400&h=200&fit=crop'
  },
  {
    id: 'featured-2',
    name: 'API Tester Pro',
    description: '专业的 API 测试和文档生成工具',
    icon: 'api',
    category: 'development',
    status: '在线',
    rating: 4.7,
    users: 8900,
    lastUpdated: '1周前',
    features: ['API测试', '文档生成', '自动化测试'],
    tags: ['API', '测试', '文档'],
    url: 'https://api-tester-pro.com',
    isBookmarked: true,
    preview: 'https://images.unsplash.com/photo-1460925895917-afdab827c52f?w=400&h=200&fit=crop'
  }
])

// 工具列表
const tools = ref<Tool[]>([
  {
    id: '1',
    name: 'Docker Desktop',
    description: '官方 Docker 桌面应用，支持 Apple Silicon 优化',
    icon: 'containers',
    category: 'development',
    status: '在线',
    rating: 4.8,
    users: 50000,
    lastUpdated: '3天前',
    features: ['容器管理', 'Kubernetes', '镜像构建'],
    tags: ['Docker', 'Container', 'Development'],
    url: 'https://docker.com/desktop',
    isBookmarked: false
  },
  {
    id: '2',
    name: 'Prometheus',
    description: '开源监控和告警系统',
    icon: 'monitoring',
    category: 'monitoring',
    status: '在线',
    rating: 4.6,
    users: 25000,
    lastUpdated: '1周前',
    features: ['指标收集', '告警规则', '数据可视化'],
    tags: ['Monitoring', 'Metrics', 'Alerting'],
    url: 'https://prometheus.io',
    isBookmarked: true
  },
  {
    id: '3',
    name: 'Security Scanner',
    description: '容器安全漏洞扫描工具',
    icon: 'shield',
    category: 'security',
    status: '在线',
    rating: 4.5,
    users: 15000,
    lastUpdated: '5天前',
    features: ['漏洞扫描', '合规检查', '安全报告'],
    tags: ['Security', 'Vulnerability', 'Compliance'],
    url: 'https://security-scanner.dev',
    isBookmarked: false
  }
])

// 自定义工具
const customTools = ref<CustomTool[]>([
  {
    id: 'custom-1',
    name: '内部 API 文档',
    description: '公司内部 API 文档和测试工具',
    icon: 'document',
    url: 'https://internal-api-docs.company.com'
  },
  {
    id: 'custom-2',
    name: '部署管道',
    description: 'CI/CD 部署管道监控面板',
    icon: 'pipeline',
    url: 'https://ci-cd.company.com'
  }
])

// 计算属性
const filteredTools = computed(() => {
  let filtered = selectedCategory.value === 'all' 
    ? tools.value 
    : tools.value.filter(tool => tool.category === selectedCategory.value)
  
  // 排序
  switch (sortBy.value) {
    case 'newest':
      // 这里应该根据实际的创建时间排序
      break
    case 'rating':
      filtered = filtered.sort((a, b) => b.rating - a.rating)
      break
    case 'name':
      filtered = filtered.sort((a, b) => a.name.localeCompare(b.name))
      break
    case 'popular':
    default:
      filtered = filtered.sort((a, b) => b.users - a.users)
      break
  }
  
  return filtered
})

// 方法
const selectCategory = (categoryId: string) => {
  selectedCategory.value = categoryId
}

const getCategoryName = (categoryId: string): string => {
  const category = toolCategories.value.find(cat => cat.id === categoryId)
  return category ? category.name : '全部工具'
}

const openTool = (tool: Tool) => {
  console.log('打开工具:', tool.name)
  // 这里可以在新窗口打开工具或显示详情
}

const launchTool = (tool: Tool) => {
  console.log('启动工具:', tool.name)
  window.open(tool.url, '_blank')
}

const bookmarkTool = (toolId: string) => {
  const tool = tools.value.find(t => t.id === toolId)
  if (tool) {
    tool.isBookmarked = !tool.isBookmarked
  }
}

const addCustomTool = () => {
  console.log('添加自定义工具')
  // 这里可以打开添加工具的对话框
}

const editCustomTool = (tool: CustomTool) => {
  console.log('编辑自定义工具:', tool.name)
}

const deleteCustomTool = (toolId: string) => {
  const index = customTools.value.findIndex(tool => tool.id === toolId)
  if (index > -1) {
    customTools.value.splice(index, 1)
  }
}

const launchCustomTool = (tool: CustomTool) => {
  console.log('启动自定义工具:', tool.name)
  window.open(tool.url, '_blank')
}

const formatNumber = (num: number): string => {
  if (num >= 1000) {
    return (num / 1000).toFixed(1) + 'K'
  }
  return num.toString()
}

// 生命周期
onMounted(() => {
  console.log('工具箱中心组件已加载')
})
</script>

<style scoped>
.toolbox-center {
  display: flex;
  flex-direction: column;
  gap: 40px;
}

/* 通用样式 */
.section-title {
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 24px;
  font-weight: 700;
  color: var(--text-primary);
  margin: 0 0 24px 0;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

/* 工具分类 */
.categories-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 20px;
}

.category-card {
  display: flex;
  align-items: center;
  gap: 16px;
  background: var(--gradient-card);
  backdrop-filter: blur(20px);
  border: 1px solid var(--border-color);
  border-radius: 16px;
  padding: 20px;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.category-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 16px 48px var(--shadow-medium);
}

.category-card.active {
  border-color: var(--accent-blue);
  background: linear-gradient(135deg, rgba(0, 122, 255, 0.1) 0%, var(--gradient-card) 100%);
}

.category-icon {
  width: 56px;
  height: 56px;
  border-radius: 16px;
  background: var(--accent-blue);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  flex-shrink: 0;
}

.category-name {
  font-size: 18px;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0 0 4px 0;
}

.category-description {
  font-size: 14px;
  color: var(--text-secondary);
  margin: 0 0 8px 0;
  line-height: 1.4;
}

.category-count {
  font-size: 12px;
  color: var(--text-tertiary);
  font-weight: 500;
}

/* 推荐工具 */
.featured-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 24px;
}

.featured-tool {
  background: var(--gradient-card);
  backdrop-filter: blur(20px);
  border: 1px solid var(--border-color);
  border-radius: 20px;
  overflow: hidden;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.featured-tool:hover {
  transform: translateY(-6px);
  box-shadow: 0 20px 60px var(--shadow-medium);
}

.tool-preview {
  position: relative;
  height: 200px;
  overflow: hidden;
}

.tool-preview img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.tool-overlay {
  position: absolute;
  inset: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.featured-tool:hover .tool-overlay {
  opacity: 1;
}

.launch-button {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 24px;
  background: var(--accent-blue);
  color: white;
  border: none;
  border-radius: 12px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
}

.launch-button:hover {
  background: var(--accent-blue);
  transform: scale(1.05);
}

.tool-info {
  padding: 20px;
}

.tool-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.tool-name {
  font-size: 20px;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0;
}

.tool-rating {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 14px;
  font-weight: 600;
  color: var(--accent-orange);
}

.tool-description {
  font-size: 14px;
  color: var(--text-secondary);
  line-height: 1.5;
  margin: 0 0 16px 0;
}

.tool-tags {
  display: flex;
  gap: 6px;
  flex-wrap: wrap;
}

.tool-tag {
  padding: 4px 8px;
  background: var(--accent-blue);
  color: white;
  border-radius: 6px;
  font-size: 11px;
  font-weight: 500;
}

/* 工具列表 */
.view-controls {
  display: flex;
  gap: 12px;
  align-items: center;
}

.sort-select {
  padding: 8px 12px;
  background: var(--gradient-card);
  border: 1px solid var(--border-color);
  border-radius: 8px;
  color: var(--text-primary);
  font-size: 14px;
  cursor: pointer;
}

.tools-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 24px;
}

.tool-card {
  display: flex;
  gap: 16px;
  background: var(--gradient-card);
  backdrop-filter: blur(20px);
  border: 1px solid var(--border-color);
  border-radius: 16px;
  padding: 20px;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.tool-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 16px 48px var(--shadow-medium);
}

.tool-icon {
  width: 56px;
  height: 56px;
  border-radius: 16px;
  background: var(--bg-tertiary);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--accent-blue);
  flex-shrink: 0;
}

.tool-content {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.tool-status {
  padding: 4px 8px;
  border-radius: 6px;
  font-size: 11px;
  font-weight: 600;
}

.tool-status.在线 {
  background: rgba(52, 199, 89, 0.1);
  color: var(--accent-green);
}

.tool-features ul {
  list-style: none;
  padding: 0;
  margin: 0 0 16px 0;
}

.tool-features li {
  font-size: 13px;
  color: var(--text-secondary);
  padding: 2px 0;
  position: relative;
  padding-left: 12px;
}

.tool-features li::before {
  content: '•';
  color: var(--accent-blue);
  position: absolute;
  left: 0;
}

.tool-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: auto;
}

.tool-meta {
  display: flex;
  gap: 12px;
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  color: var(--text-tertiary);
}

.tool-actions {
  display: flex;
  gap: 8px;
}

.action-button {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 6px 12px;
  border: 1px solid var(--border-color);
  border-radius: 8px;
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.action-button.secondary {
  background: transparent;
  color: var(--text-secondary);
}

.action-button.secondary:hover {
  background: var(--bg-tertiary);
  color: var(--text-primary);
}

.action-button.primary {
  background: var(--accent-blue);
  color: white;
  border-color: var(--accent-blue);
}

.action-button.primary:hover {
  background: var(--accent-blue);
  transform: scale(1.05);
}

/* 自定义工具 */
.add-tool-button {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 20px;
  background: var(--accent-green);
  color: white;
  border: none;
  border-radius: 12px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
}

.add-tool-button:hover {
  background: var(--accent-green);
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(52, 199, 89, 0.3);
}

.custom-tools-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 20px;
}

.custom-tool-card {
  background: var(--gradient-card);
  backdrop-filter: blur(20px);
  border: 1px solid var(--border-color);
  border-radius: 16px;
  padding: 20px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.custom-tool-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 16px 48px var(--shadow-medium);
}

.custom-tool-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.custom-tool-icon {
  width: 40px;
  height: 40px;
  border-radius: 12px;
  background: var(--accent-green);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
}

.custom-tool-actions {
  display: flex;
  gap: 4px;
}

.tool-action {
  width: 28px;
  height: 28px;
  border-radius: 6px;
  background: var(--bg-tertiary);
  border: 1px solid var(--border-color);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  color: var(--text-tertiary);
}

.tool-action:hover {
  background: var(--bg-secondary);
  color: var(--text-secondary);
}

.custom-tool-name {
  font-size: 16px;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0 0 8px 0;
}

.custom-tool-description {
  font-size: 14px;
  color: var(--text-secondary);
  margin: 0 0 12px 0;
  line-height: 1.4;
}

.custom-tool-url {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 12px;
  color: var(--text-tertiary);
  margin-bottom: 16px;
}

.launch-custom-tool {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 16px;
  background: var(--accent-green);
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 12px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  width: 100%;
  justify-content: center;
}

.launch-custom-tool:hover {
  background: var(--accent-green);
  transform: scale(1.02);
}

.add-tool-card {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: var(--gradient-card);
  backdrop-filter: blur(20px);
  border: 2px dashed var(--border-color);
  border-radius: 16px;
  padding: 40px 20px;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  min-height: 200px;
}

.add-tool-card:hover {
  border-color: var(--accent-blue);
  background: linear-gradient(135deg, rgba(0, 122, 255, 0.05) 0%, var(--gradient-card) 100%);
  transform: translateY(-4px);
}

.add-tool-icon {
  color: var(--text-tertiary);
  margin-bottom: 12px;
}

.add-tool-text {
  font-size: 14px;
  color: var(--text-secondary);
  font-weight: 500;
  margin: 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .categories-grid,
  .featured-grid,
  .tools-grid,
  .custom-tools-grid {
    grid-template-columns: 1fr;
  }
  
  .tool-card {
    flex-direction: column;
    text-align: center;
  }
  
  .tool-footer {
    flex-direction: column;
    gap: 12px;
  }
}
</style>
