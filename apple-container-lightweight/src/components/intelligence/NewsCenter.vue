<template>
  <div class="news-center">
    <!-- 头条新闻 -->
    <div class="featured-news">
      <h2 class="section-title">
        <AppleIcon name="sparkles" size="large" />
        头条新闻
      </h2>
      <div class="featured-article" v-if="featuredArticle">
        <div class="featured-image">
          <img :src="featuredArticle.image" :alt="featuredArticle.title" />
          <div class="featured-overlay">
            <span class="featured-category">{{ featuredArticle.category }}</span>
          </div>
        </div>
        <div class="featured-content">
          <h3 class="featured-title">{{ featuredArticle.title }}</h3>
          <p class="featured-summary">{{ featuredArticle.summary }}</p>
          <div class="featured-meta">
            <span class="author">{{ featuredArticle.author }}</span>
            <span class="date">{{ featuredArticle.publishedAt }}</span>
            <div class="reading-time">
              <AppleIcon name="clock" size="small" />
              <span>{{ featuredArticle.readingTime }} 分钟阅读</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 新闻分类 -->
    <div class="news-categories">
      <button 
        v-for="category in categories" 
        :key="category.id"
        class="category-tab"
        :class="{ active: selectedCategory === category.id }"
        @click="selectCategory(category.id)"
      >
        <AppleIcon :name="category.icon" size="medium" />
        <span>{{ category.name }}</span>
        <span class="category-badge">{{ category.count }}</span>
      </button>
    </div>

    <!-- 新闻列表 -->
    <div class="news-grid">
      <article 
        v-for="article in filteredArticles" 
        :key="article.id"
        class="news-card"
        @click="openArticle(article)"
      >
        <div class="news-image">
          <img :src="article.image" :alt="article.title" />
          <div class="news-overlay">
            <span class="news-category">{{ article.category }}</span>
            <button class="bookmark-button" @click.stop="toggleBookmark(article.id)">
              <AppleIcon 
                name="bookmark" 
                size="small" 
                :variant="article.isBookmarked ? 'fill' : 'regular'"
                :class="{ bookmarked: article.isBookmarked }"
              />
            </button>
          </div>
        </div>
        
        <div class="news-content">
          <h3 class="news-title">{{ article.title }}</h3>
          <p class="news-summary">{{ article.summary }}</p>
          
          <div class="news-tags">
            <span 
              v-for="tag in article.tags.slice(0, 2)" 
              :key="tag"
              class="news-tag"
            >
              {{ tag }}
            </span>
          </div>
          
          <div class="news-meta">
            <div class="meta-left">
              <span class="author">{{ article.author }}</span>
              <span class="date">{{ article.publishedAt }}</span>
            </div>
            <div class="meta-right">
              <div class="reading-time">
                <AppleIcon name="clock" size="small" />
                <span>{{ article.readingTime }}min</span>
              </div>
              <div class="engagement">
                <AppleIcon name="heart" size="small" />
                <span>{{ article.likes }}</span>
              </div>
            </div>
          </div>
        </div>
      </article>
    </div>

    <!-- 趋势话题 -->
    <div class="trending-topics">
      <h2 class="section-title">
        <AppleIcon name="trending" size="large" />
        趋势话题
      </h2>
      <div class="topics-grid">
        <div 
          v-for="topic in trendingTopics" 
          :key="topic.id"
          class="topic-card"
          @click="searchTopic(topic.name)"
        >
          <div class="topic-rank">#{{ topic.rank }}</div>
          <div class="topic-content">
            <h4 class="topic-name">{{ topic.name }}</h4>
            <p class="topic-description">{{ topic.description }}</p>
            <div class="topic-stats">
              <span class="topic-posts">{{ topic.posts }} 篇文章</span>
              <span class="topic-growth" :class="{ positive: topic.growth > 0 }">
                {{ topic.growth > 0 ? '+' : '' }}{{ topic.growth }}%
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import AppleIcon from '../AppleIcon.vue'

// 接口定义
interface Article {
  id: string
  title: string
  summary: string
  content: string
  image: string
  category: string
  tags: string[]
  author: string
  publishedAt: string
  readingTime: number
  likes: number
  isBookmarked: boolean
}

interface TrendingTopic {
  id: string
  name: string
  description: string
  rank: number
  posts: number
  growth: number
}

// 响应式数据
const selectedCategory = ref('all')

// 分类配置
const categories = ref([
  { id: 'all', name: '全部', icon: 'news', count: 156 },
  { id: 'apple', name: 'Apple', icon: 'apple', count: 45 },
  { id: 'containers', name: '容器技术', icon: 'containers', count: 38 },
  { id: 'ai', name: 'AI/ML', icon: 'brain', count: 29 },
  { id: 'development', name: '开发', icon: 'toolbox', count: 44 }
])

// 头条文章
const featuredArticle = ref<Article>({
  id: 'featured-1',
  title: 'Apple 发布 macOS Tahoe 26.0：原生容器技术革命性突破',
  summary: '苹果在最新的 macOS Tahoe 26.0 中引入了革命性的原生容器技术，为开发者提供了前所未有的性能和安全性。这项技术将彻底改变容器化应用的开发和部署方式。',
  content: '',
  image: 'https://images.unsplash.com/photo-1611224923853-80b023f02d71?w=800&h=400&fit=crop',
  category: 'Apple',
  tags: ['macOS', 'Container', 'Apple Silicon'],
  author: 'Tim Cook',
  publishedAt: '2小时前',
  readingTime: 8,
  likes: 1247,
  isBookmarked: false
})

// 新闻文章
const articles = ref<Article[]>([
  {
    id: '1',
    title: 'Docker Desktop 支持 Apple Silicon 原生优化',
    summary: 'Docker 宣布对 Apple Silicon 芯片进行深度优化，性能提升高达 300%',
    content: '',
    image: 'https://images.unsplash.com/photo-1618477247222-acbdb0e159b3?w=400&h=200&fit=crop',
    category: '容器技术',
    tags: ['Docker', 'Apple Silicon', 'Performance'],
    author: 'Docker Team',
    publishedAt: '4小时前',
    readingTime: 5,
    likes: 892,
    isBookmarked: true
  },
  {
    id: '2',
    title: 'Kubernetes 1.29 发布：增强 ARM64 支持',
    summary: 'Kubernetes 最新版本大幅改进了对 ARM64 架构的支持',
    content: '',
    image: 'https://images.unsplash.com/photo-1667372393119-3d4c48d07fc9?w=400&h=200&fit=crop',
    category: '容器技术',
    tags: ['Kubernetes', 'ARM64', 'Cloud Native'],
    author: 'CNCF',
    publishedAt: '6小时前',
    readingTime: 7,
    likes: 654,
    isBookmarked: false
  },
  {
    id: '3',
    title: 'Apple Intelligence 集成开发者工具链',
    summary: 'Apple Intelligence 现已集成到 Xcode 和开发者工具中',
    content: '',
    image: 'https://images.unsplash.com/photo-1677442136019-21780ecad995?w=400&h=200&fit=crop',
    category: 'AI',
    tags: ['Apple Intelligence', 'Xcode', 'AI'],
    author: 'Apple Developer',
    publishedAt: '8小时前',
    readingTime: 6,
    likes: 1123,
    isBookmarked: false
  },
  {
    id: '4',
    title: 'Swift 6.0 正式发布：并发编程新特性',
    summary: 'Swift 6.0 带来了强大的并发编程特性和性能优化',
    content: '',
    image: 'https://images.unsplash.com/photo-1555066931-4365d14bab8c?w=400&h=200&fit=crop',
    category: '开发',
    tags: ['Swift', 'Concurrency', 'Programming'],
    author: 'Swift Team',
    publishedAt: '12小时前',
    readingTime: 9,
    likes: 2156,
    isBookmarked: true
  },
  {
    id: '5',
    title: 'WebAssembly 在容器化应用中的应用',
    summary: '探索 WebAssembly 如何改变容器化应用的开发模式',
    content: '',
    image: 'https://images.unsplash.com/photo-1558494949-ef010cbdcc31?w=400&h=200&fit=crop',
    category: '容器技术',
    tags: ['WebAssembly', 'Container', 'Performance'],
    author: 'WebAssembly Community',
    publishedAt: '1天前',
    readingTime: 11,
    likes: 789,
    isBookmarked: false
  },
  {
    id: '6',
    title: 'Apple Vision Pro 开发者生态更新',
    summary: 'Apple Vision Pro 平台迎来重大开发者工具更新',
    content: '',
    image: 'https://images.unsplash.com/photo-1592478411213-6153e4ebc696?w=400&h=200&fit=crop',
    category: 'Apple',
    tags: ['Vision Pro', 'visionOS', 'AR/VR'],
    author: 'Apple',
    publishedAt: '1天前',
    readingTime: 8,
    likes: 1567,
    isBookmarked: false
  }
])

// 趋势话题
const trendingTopics = ref<TrendingTopic[]>([
  {
    id: '1',
    name: 'Apple Silicon 优化',
    description: '针对 Apple Silicon 芯片的软件优化技术',
    rank: 1,
    posts: 234,
    growth: 15.6
  },
  {
    id: '2',
    name: '容器安全',
    description: '容器化应用的安全最佳实践',
    rank: 2,
    posts: 189,
    growth: 8.3
  },
  {
    id: '3',
    name: 'Serverless 架构',
    description: '无服务器架构的发展趋势',
    rank: 3,
    posts: 156,
    growth: -2.1
  },
  {
    id: '4',
    name: 'AI 辅助开发',
    description: '人工智能在软件开发中的应用',
    rank: 4,
    posts: 298,
    growth: 23.7
  }
])

// 计算属性
const filteredArticles = computed(() => {
  if (selectedCategory.value === 'all') {
    return articles.value
  }
  return articles.value.filter(article => 
    article.category === categories.value.find(cat => cat.id === selectedCategory.value)?.name
  )
})

// 方法
const selectCategory = (categoryId: string) => {
  selectedCategory.value = categoryId
}

const openArticle = (article: Article) => {
  console.log('打开文章:', article.title)
  // 这里可以打开文章详情页或外部链接
}

const toggleBookmark = (articleId: string) => {
  const article = articles.value.find(art => art.id === articleId)
  if (article) {
    article.isBookmarked = !article.isBookmarked
  }
}

const searchTopic = (topicName: string) => {
  console.log('搜索话题:', topicName)
  // 实现话题搜索功能
}

// 生命周期
onMounted(() => {
  console.log('资讯中心组件已加载')
})
</script>

<style scoped>
.news-center {
  display: flex;
  flex-direction: column;
  gap: 32px;
}

/* 通用样式 */
.section-title {
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 24px;
  font-weight: 700;
  color: var(--text-primary);
  margin: 0 0 20px 0;
}

/* 头条新闻 */
.featured-article {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 24px;
  background: var(--gradient-card);
  backdrop-filter: blur(20px);
  border: 1px solid var(--border-color);
  border-radius: 20px;
  overflow: hidden;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.featured-article:hover {
  transform: translateY(-4px);
  box-shadow: 0 20px 60px var(--shadow-medium);
}

.featured-image {
  position: relative;
  height: 300px;
  overflow: hidden;
}

.featured-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.featured-overlay {
  position: absolute;
  top: 16px;
  left: 16px;
}

.featured-category {
  padding: 6px 12px;
  background: var(--accent-blue);
  color: white;
  border-radius: 8px;
  font-size: 12px;
  font-weight: 600;
}

.featured-content {
  padding: 24px;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.featured-title {
  font-size: 28px;
  font-weight: 700;
  color: var(--text-primary);
  margin: 0 0 16px 0;
  line-height: 1.3;
}

.featured-summary {
  font-size: 16px;
  color: var(--text-secondary);
  line-height: 1.6;
  margin: 0 0 20px 0;
}

.featured-meta {
  display: flex;
  align-items: center;
  gap: 16px;
  font-size: 14px;
  color: var(--text-tertiary);
}

.reading-time {
  display: flex;
  align-items: center;
  gap: 4px;
  margin-left: auto;
}

/* 新闻分类 */
.news-categories {
  display: flex;
  gap: 12px;
  overflow-x: auto;
  padding-bottom: 8px;
}

.category-tab {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 16px;
  background: var(--gradient-card);
  backdrop-filter: blur(20px);
  border: 1px solid var(--border-color);
  border-radius: 12px;
  color: var(--text-secondary);
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  white-space: nowrap;
}

.category-tab:hover {
  color: var(--text-primary);
  transform: translateY(-2px);
  box-shadow: 0 8px 24px var(--shadow-light);
}

.category-tab.active {
  background: var(--accent-blue);
  color: white;
  border-color: var(--accent-blue);
}

.category-badge {
  background: rgba(255, 255, 255, 0.2);
  padding: 2px 6px;
  border-radius: 8px;
  font-size: 11px;
  font-weight: 600;
}

/* 新闻网格 */
.news-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 24px;
}

.news-card {
  background: var(--gradient-card);
  backdrop-filter: blur(20px);
  border: 1px solid var(--border-color);
  border-radius: 16px;
  overflow: hidden;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.news-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 16px 48px var(--shadow-medium);
}

.news-image {
  position: relative;
  height: 200px;
  overflow: hidden;
}

.news-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.news-overlay {
  position: absolute;
  top: 12px;
  left: 12px;
  right: 12px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.news-category {
  padding: 4px 8px;
  background: var(--accent-blue);
  color: white;
  border-radius: 6px;
  font-size: 11px;
  font-weight: 600;
}

.bookmark-button {
  width: 32px;
  height: 32px;
  border-radius: 8px;
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
}

.bookmark-button:hover {
  background: white;
  transform: scale(1.1);
}

.bookmarked {
  color: var(--accent-orange);
}

.news-content {
  padding: 20px;
}

.news-title {
  font-size: 18px;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0 0 12px 0;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.news-summary {
  font-size: 14px;
  color: var(--text-secondary);
  line-height: 1.5;
  margin: 0 0 16px 0;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.news-tags {
  display: flex;
  gap: 6px;
  margin-bottom: 16px;
}

.news-tag {
  padding: 4px 8px;
  background: var(--accent-blue);
  color: white;
  border-radius: 6px;
  font-size: 11px;
  font-weight: 500;
}

.news-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 12px;
  color: var(--text-tertiary);
}

.meta-left {
  display: flex;
  gap: 8px;
}

.meta-right {
  display: flex;
  gap: 12px;
}

.reading-time,
.engagement {
  display: flex;
  align-items: center;
  gap: 4px;
}

/* 趋势话题 */
.topics-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 20px;
}

.topic-card {
  display: flex;
  gap: 16px;
  background: var(--gradient-card);
  backdrop-filter: blur(20px);
  border: 1px solid var(--border-color);
  border-radius: 16px;
  padding: 20px;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.topic-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 16px 48px var(--shadow-medium);
}

.topic-rank {
  width: 40px;
  height: 40px;
  border-radius: 12px;
  background: var(--accent-blue);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 18px;
  font-weight: 700;
  flex-shrink: 0;
}

.topic-name {
  font-size: 16px;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0 0 8px 0;
}

.topic-description {
  font-size: 14px;
  color: var(--text-secondary);
  margin: 0 0 12px 0;
  line-height: 1.4;
}

.topic-stats {
  display: flex;
  gap: 12px;
  font-size: 12px;
}

.topic-posts {
  color: var(--text-tertiary);
}

.topic-growth {
  font-weight: 600;
  color: var(--accent-red);
}

.topic-growth.positive {
  color: var(--accent-green);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .featured-article {
    grid-template-columns: 1fr;
  }
  
  .featured-image {
    height: 200px;
  }
  
  .featured-title {
    font-size: 24px;
  }
  
  .news-grid {
    grid-template-columns: 1fr;
  }
  
  .news-categories {
    flex-wrap: wrap;
  }
}
</style>
