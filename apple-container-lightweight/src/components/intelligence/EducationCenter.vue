<template>
  <div class="education-center">
    <!-- 学习路径 -->
    <div class="learning-paths">
      <h2 class="section-title">
        <AppleIcon name="graduation" size="large" />
        推荐学习路径
      </h2>
      <div class="paths-grid">
        <div 
          v-for="path in learningPaths" 
          :key="path.id"
          class="path-card"
          @click="selectPath(path)"
        >
          <div class="path-header">
            <div class="path-icon">
              <AppleIcon :name="path.icon" size="large" variant="fill" />
            </div>
            <div class="path-level" :class="path.level.toLowerCase()">
              {{ path.level }}
            </div>
          </div>
          <div class="path-content">
            <h3 class="path-title">{{ path.title }}</h3>
            <p class="path-description">{{ path.description }}</p>
            <div class="path-stats">
              <div class="stat-item">
                <AppleIcon name="clock" size="small" />
                <span>{{ path.duration }}</span>
              </div>
              <div class="stat-item">
                <AppleIcon name="users" size="small" />
                <span>{{ path.students }} 学员</span>
              </div>
              <div class="stat-item">
                <AppleIcon name="star" size="small" />
                <span>{{ path.rating }}</span>
              </div>
            </div>
            <div class="path-progress">
              <div class="progress-bar">
                <div 
                  class="progress-fill" 
                  :style="{ width: path.progress + '%' }"
                ></div>
              </div>
              <span class="progress-text">{{ path.progress }}% 完成</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 难度筛选 -->
    <div class="difficulty-filter">
      <h3>按难度筛选</h3>
      <div class="filter-buttons">
        <button 
          v-for="level in difficultyLevels" 
          :key="level"
          class="filter-button"
          :class="{ active: selectedDifficulty === level }"
          @click="selectDifficulty(level)"
        >
          {{ level }}
        </button>
      </div>
    </div>

    <!-- 教程资源 -->
    <div class="tutorials-section">
      <div class="section-header">
        <h2 class="section-title">
          <AppleIcon name="library" size="large" />
          教程资源
        </h2>
        <div class="view-toggle">
          <button 
            class="toggle-button"
            :class="{ active: viewMode === 'grid' }"
            @click="viewMode = 'grid'"
          >
            <AppleIcon name="grid" size="small" />
          </button>
          <button 
            class="toggle-button"
            :class="{ active: viewMode === 'list' }"
            @click="viewMode = 'list'"
          >
            <AppleIcon name="list" size="small" />
          </button>
        </div>
      </div>

      <div class="tutorials-container" :class="viewMode">
        <div 
          v-for="tutorial in filteredTutorials" 
          :key="tutorial.id"
          class="tutorial-card"
          @click="openTutorial(tutorial)"
        >
          <div class="tutorial-thumbnail">
            <img :src="tutorial.thumbnail" :alt="tutorial.title" />
            <div class="tutorial-overlay">
              <div class="tutorial-type" :class="tutorial.type.toLowerCase()">
                <AppleIcon :name="getTutorialIcon(tutorial.type)" size="small" />
                <span>{{ tutorial.type }}</span>
              </div>
              <div class="tutorial-duration">{{ tutorial.duration }}</div>
            </div>
            <button class="play-button" v-if="tutorial.type === '视频'">
              <AppleIcon name="play" size="medium" />
            </button>
          </div>
          
          <div class="tutorial-content">
            <div class="tutorial-meta">
              <span class="tutorial-category">{{ tutorial.category }}</span>
              <div class="tutorial-difficulty" :class="tutorial.difficulty.toLowerCase()">
                {{ tutorial.difficulty }}
              </div>
            </div>
            
            <h3 class="tutorial-title">{{ tutorial.title }}</h3>
            <p class="tutorial-description">{{ tutorial.description }}</p>
            
            <div class="tutorial-tags">
              <span 
                v-for="tag in tutorial.tags.slice(0, 3)" 
                :key="tag"
                class="tutorial-tag"
              >
                {{ tag }}
              </span>
            </div>
            
            <div class="tutorial-footer">
              <div class="tutorial-author">
                <img :src="tutorial.author.avatar" :alt="tutorial.author.name" />
                <span>{{ tutorial.author.name }}</span>
              </div>
              <div class="tutorial-stats">
                <div class="stat-item">
                  <AppleIcon name="eye" size="small" />
                  <span>{{ formatNumber(tutorial.views) }}</span>
                </div>
                <div class="stat-item">
                  <AppleIcon name="heart" size="small" />
                  <span>{{ tutorial.likes }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 实践项目 -->
    <div class="projects-section">
      <h2 class="section-title">
        <AppleIcon name="toolbox" size="large" />
        实践项目
      </h2>
      <div class="projects-grid">
        <div 
          v-for="project in practiceProjects" 
          :key="project.id"
          class="project-card"
          @click="startProject(project)"
        >
          <div class="project-header">
            <div class="project-icon">
              <AppleIcon :name="project.icon" size="large" />
            </div>
            <div class="project-difficulty" :class="project.difficulty.toLowerCase()">
              {{ project.difficulty }}
            </div>
          </div>
          
          <div class="project-content">
            <h3 class="project-title">{{ project.title }}</h3>
            <p class="project-description">{{ project.description }}</p>
            
            <div class="project-skills">
              <h4>你将学到：</h4>
              <ul>
                <li v-for="skill in project.skills" :key="skill">{{ skill }}</li>
              </ul>
            </div>
            
            <div class="project-footer">
              <div class="project-stats">
                <span class="project-time">
                  <AppleIcon name="clock" size="small" />
                  {{ project.estimatedTime }}
                </span>
                <span class="project-participants">
                  <AppleIcon name="users" size="small" />
                  {{ project.participants }} 人参与
                </span>
              </div>
              <button class="start-button">
                <AppleIcon name="play" size="small" />
                开始项目
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import AppleIcon from '../AppleIcon.vue'

// 接口定义
interface LearningPath {
  id: string
  title: string
  description: string
  icon: string
  level: string
  duration: string
  students: number
  rating: number
  progress: number
}

interface Tutorial {
  id: string
  title: string
  description: string
  thumbnail: string
  type: string
  category: string
  difficulty: string
  duration: string
  tags: string[]
  author: {
    name: string
    avatar: string
  }
  views: number
  likes: number
}

interface PracticeProject {
  id: string
  title: string
  description: string
  icon: string
  difficulty: string
  estimatedTime: string
  participants: number
  skills: string[]
}

// 响应式数据
const selectedDifficulty = ref('全部')
const viewMode = ref<'grid' | 'list'>('grid')

// 难度级别
const difficultyLevels = ref(['全部', '初级', '中级', '高级'])

// 学习路径
const learningPaths = ref<LearningPath[]>([
  {
    id: '1',
    title: 'Apple Silicon 容器开发',
    description: '从零开始学习在 Apple Silicon 上进行容器化开发',
    icon: 'apple',
    level: '初级',
    duration: '6周',
    students: 1247,
    rating: 4.8,
    progress: 35
  },
  {
    id: '2',
    title: 'Kubernetes 实战指南',
    description: '深入学习 Kubernetes 集群管理和应用部署',
    icon: 'containers',
    level: '中级',
    duration: '8周',
    students: 892,
    rating: 4.9,
    progress: 0
  },
  {
    id: '3',
    title: 'Docker 高级优化',
    description: '掌握 Docker 性能优化和最佳实践',
    icon: 'settings',
    level: '高级',
    duration: '4周',
    students: 456,
    rating: 4.7,
    progress: 100
  }
])

// 教程资源
const tutorials = ref<Tutorial[]>([
  {
    id: '1',
    title: 'Apple Silicon 上的 Docker 性能优化',
    description: '学习如何在 Apple Silicon 芯片上优化 Docker 容器性能',
    thumbnail: 'https://images.unsplash.com/photo-1618477247222-acbdb0e159b3?w=400&h=225&fit=crop',
    type: '视频',
    category: '容器技术',
    difficulty: '中级',
    duration: '45分钟',
    tags: ['Docker', 'Apple Silicon', '性能优化'],
    author: {
      name: 'John Doe',
      avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=40&h=40&fit=crop&crop=face'
    },
    views: 12500,
    likes: 892
  },
  {
    id: '2',
    title: 'Kubernetes 集群搭建完整指南',
    description: '从零开始搭建生产级 Kubernetes 集群',
    thumbnail: 'https://images.unsplash.com/photo-1667372393119-3d4c48d07fc9?w=400&h=225&fit=crop',
    type: '文档',
    category: '容器编排',
    difficulty: '高级',
    duration: '2小时阅读',
    tags: ['Kubernetes', '集群管理', 'DevOps'],
    author: {
      name: 'Jane Smith',
      avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=40&h=40&fit=crop&crop=face'
    },
    views: 8900,
    likes: 654
  },
  {
    id: '3',
    title: 'Swift 容器化应用开发',
    description: '使用 Swift 开发云原生应用的最佳实践',
    thumbnail: 'https://images.unsplash.com/photo-1555066931-4365d14bab8c?w=400&h=225&fit=crop',
    type: '视频',
    category: 'Swift 开发',
    difficulty: '初级',
    duration: '1小时30分钟',
    tags: ['Swift', '云原生', 'iOS'],
    author: {
      name: 'Apple Developer',
      avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=40&h=40&fit=crop&crop=face'
    },
    views: 15600,
    likes: 1234
  }
])

// 实践项目
const practiceProjects = ref<PracticeProject[]>([
  {
    id: '1',
    title: '构建微服务架构',
    description: '使用 Docker 和 Kubernetes 构建完整的微服务应用',
    icon: 'containers',
    difficulty: '中级',
    estimatedTime: '3-4小时',
    participants: 567,
    skills: [
      '微服务设计模式',
      'Docker 容器化',
      'Kubernetes 部署',
      '服务网格配置'
    ]
  },
  {
    id: '2',
    title: 'CI/CD 流水线搭建',
    description: '从代码提交到生产部署的完整自动化流程',
    icon: 'toolbox',
    difficulty: '高级',
    estimatedTime: '4-5小时',
    participants: 234,
    skills: [
      'GitHub Actions',
      '自动化测试',
      '容器镜像构建',
      '生产环境部署'
    ]
  }
])

// 计算属性
const filteredTutorials = computed(() => {
  if (selectedDifficulty.value === '全部') {
    return tutorials.value
  }
  return tutorials.value.filter(tutorial => tutorial.difficulty === selectedDifficulty.value)
})

// 方法
const selectPath = (path: LearningPath) => {
  console.log('选择学习路径:', path.title)
}

const selectDifficulty = (level: string) => {
  selectedDifficulty.value = level
}

const openTutorial = (tutorial: Tutorial) => {
  console.log('打开教程:', tutorial.title)
}

const startProject = (project: PracticeProject) => {
  console.log('开始项目:', project.title)
}

const getTutorialIcon = (type: string): string => {
  switch (type) {
    case '视频':
      return 'play'
    case '文档':
      return 'document'
    case '实践':
      return 'toolbox'
    default:
      return 'library'
  }
}

const formatNumber = (num: number): string => {
  if (num >= 1000) {
    return (num / 1000).toFixed(1) + 'K'
  }
  return num.toString()
}

// 生命周期
onMounted(() => {
  console.log('教育中心组件已加载')
})
</script>

<style scoped>
.education-center {
  display: flex;
  flex-direction: column;
  gap: 40px;
}

/* 通用样式 */
.section-title {
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 24px;
  font-weight: 700;
  color: var(--text-primary);
  margin: 0 0 24px 0;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

/* 学习路径 */
.paths-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 24px;
}

.path-card {
  background: var(--gradient-card);
  backdrop-filter: blur(20px);
  border: 1px solid var(--border-color);
  border-radius: 20px;
  padding: 24px;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.path-card:hover {
  transform: translateY(-6px);
  box-shadow: 0 20px 60px var(--shadow-medium);
}

.path-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.path-icon {
  width: 56px;
  height: 56px;
  border-radius: 16px;
  background: var(--accent-blue);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
}

.path-level {
  padding: 6px 12px;
  border-radius: 8px;
  font-size: 12px;
  font-weight: 600;
}

.path-level.初级 {
  background: rgba(52, 199, 89, 0.1);
  color: var(--accent-green);
}

.path-level.中级 {
  background: rgba(255, 149, 0, 0.1);
  color: var(--accent-orange);
}

.path-level.高级 {
  background: rgba(255, 59, 48, 0.1);
  color: var(--accent-red);
}

.path-title {
  font-size: 20px;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0 0 12px 0;
}

.path-description {
  font-size: 14px;
  color: var(--text-secondary);
  line-height: 1.5;
  margin: 0 0 20px 0;
}

.path-stats {
  display: flex;
  gap: 16px;
  margin-bottom: 20px;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  color: var(--text-tertiary);
}

.path-progress {
  display: flex;
  align-items: center;
  gap: 12px;
}

.progress-bar {
  flex: 1;
  height: 6px;
  background: var(--bg-tertiary);
  border-radius: 3px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: var(--accent-blue);
  border-radius: 3px;
  transition: width 0.3s ease;
}

.progress-text {
  font-size: 12px;
  font-weight: 600;
  color: var(--text-secondary);
  min-width: 60px;
}

/* 难度筛选 */
.difficulty-filter {
  background: var(--gradient-card);
  backdrop-filter: blur(20px);
  border: 1px solid var(--border-color);
  border-radius: 16px;
  padding: 20px;
}

.difficulty-filter h3 {
  font-size: 16px;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0 0 16px 0;
}

.filter-buttons {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.filter-button {
  padding: 8px 16px;
  background: var(--bg-tertiary);
  border: 1px solid var(--border-color);
  border-radius: 8px;
  color: var(--text-secondary);
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.filter-button:hover {
  color: var(--text-primary);
  background: var(--bg-secondary);
}

.filter-button.active {
  background: var(--accent-blue);
  color: white;
  border-color: var(--accent-blue);
}

/* 视图切换 */
.view-toggle {
  display: flex;
  gap: 4px;
  background: var(--bg-tertiary);
  border-radius: 8px;
  padding: 4px;
}

.toggle-button {
  width: 32px;
  height: 32px;
  border-radius: 6px;
  background: transparent;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  color: var(--text-tertiary);
}

.toggle-button:hover {
  color: var(--text-secondary);
}

.toggle-button.active {
  background: var(--accent-blue);
  color: white;
}

/* 教程容器 */
.tutorials-container.grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 24px;
}

.tutorials-container.list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.tutorial-card {
  background: var(--gradient-card);
  backdrop-filter: blur(20px);
  border: 1px solid var(--border-color);
  border-radius: 16px;
  overflow: hidden;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.tutorial-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 16px 48px var(--shadow-medium);
}

.tutorials-container.list .tutorial-card {
  display: flex;
  flex-direction: row;
}

.tutorials-container.list .tutorial-thumbnail {
  width: 200px;
  flex-shrink: 0;
}

.tutorial-thumbnail {
  position: relative;
  height: 200px;
  overflow: hidden;
}

.tutorial-thumbnail img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.tutorial-overlay {
  position: absolute;
  top: 12px;
  left: 12px;
  right: 12px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.tutorial-type {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 4px 8px;
  background: rgba(0, 0, 0, 0.7);
  color: white;
  border-radius: 6px;
  font-size: 11px;
  font-weight: 600;
}

.tutorial-duration {
  padding: 4px 8px;
  background: rgba(0, 0, 0, 0.7);
  color: white;
  border-radius: 6px;
  font-size: 11px;
  font-weight: 600;
}

.play-button {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 56px;
  height: 56px;
  border-radius: 50%;
  background: rgba(0, 122, 255, 0.9);
  backdrop-filter: blur(10px);
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  cursor: pointer;
  transition: all 0.2s ease;
}

.play-button:hover {
  background: var(--accent-blue);
  transform: translate(-50%, -50%) scale(1.1);
}

.tutorial-content {
  padding: 20px;
}

.tutorial-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.tutorial-category {
  font-size: 12px;
  color: var(--accent-blue);
  font-weight: 600;
}

.tutorial-difficulty {
  padding: 4px 8px;
  border-radius: 6px;
  font-size: 11px;
  font-weight: 600;
}

.tutorial-difficulty.初级 {
  background: rgba(52, 199, 89, 0.1);
  color: var(--accent-green);
}

.tutorial-difficulty.中级 {
  background: rgba(255, 149, 0, 0.1);
  color: var(--accent-orange);
}

.tutorial-difficulty.高级 {
  background: rgba(255, 59, 48, 0.1);
  color: var(--accent-red);
}

.tutorial-title {
  font-size: 18px;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0 0 12px 0;
  line-height: 1.4;
}

.tutorial-description {
  font-size: 14px;
  color: var(--text-secondary);
  line-height: 1.5;
  margin: 0 0 16px 0;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.tutorial-tags {
  display: flex;
  gap: 6px;
  margin-bottom: 16px;
  flex-wrap: wrap;
}

.tutorial-tag {
  padding: 4px 8px;
  background: var(--accent-blue);
  color: white;
  border-radius: 6px;
  font-size: 11px;
  font-weight: 500;
}

.tutorial-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.tutorial-author {
  display: flex;
  align-items: center;
  gap: 8px;
}

.tutorial-author img {
  width: 24px;
  height: 24px;
  border-radius: 50%;
}

.tutorial-author span {
  font-size: 12px;
  color: var(--text-secondary);
  font-weight: 500;
}

.tutorial-stats {
  display: flex;
  gap: 12px;
}

/* 实践项目 */
.projects-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 24px;
}

.project-card {
  background: var(--gradient-card);
  backdrop-filter: blur(20px);
  border: 1px solid var(--border-color);
  border-radius: 20px;
  padding: 24px;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.project-card:hover {
  transform: translateY(-6px);
  box-shadow: 0 20px 60px var(--shadow-medium);
}

.project-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.project-icon {
  width: 56px;
  height: 56px;
  border-radius: 16px;
  background: var(--accent-green);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
}

.project-difficulty {
  padding: 6px 12px;
  border-radius: 8px;
  font-size: 12px;
  font-weight: 600;
}

.project-title {
  font-size: 20px;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0 0 12px 0;
}

.project-description {
  font-size: 14px;
  color: var(--text-secondary);
  line-height: 1.5;
  margin: 0 0 20px 0;
}

.project-skills h4 {
  font-size: 14px;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0 0 8px 0;
}

.project-skills ul {
  list-style: none;
  padding: 0;
  margin: 0 0 20px 0;
}

.project-skills li {
  font-size: 13px;
  color: var(--text-secondary);
  padding: 4px 0;
  position: relative;
  padding-left: 16px;
}

.project-skills li::before {
  content: '•';
  color: var(--accent-blue);
  position: absolute;
  left: 0;
}

.project-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.project-stats {
  display: flex;
  gap: 16px;
}

.project-time,
.project-participants {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  color: var(--text-tertiary);
}

.start-button {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 10px 20px;
  background: var(--accent-green);
  color: white;
  border: none;
  border-radius: 10px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
}

.start-button:hover {
  background: var(--accent-green);
  transform: scale(1.05);
  box-shadow: 0 4px 12px rgba(52, 199, 89, 0.3);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .paths-grid,
  .projects-grid {
    grid-template-columns: 1fr;
  }
  
  .tutorials-container.grid {
    grid-template-columns: 1fr;
  }
  
  .tutorials-container.list .tutorial-card {
    flex-direction: column;
  }
  
  .tutorials-container.list .tutorial-thumbnail {
    width: 100%;
  }
  
  .filter-buttons {
    justify-content: center;
  }
}
</style>
