<template>
  <div class="networks-page">
    <div class="page-header">
      <div class="header-content">
        <h1 class="page-title">
          <span class="title-icon">🌐</span>
          网络管理
        </h1>
        <p class="page-description">Docker网络配置和管理</p>
      </div>
    </div>

    <div class="coming-soon">
      <div class="coming-soon-icon">🚧</div>
      <h2>功能开发中</h2>
      <p>网络管理功能正在开发中，敬请期待...</p>
    </div>
  </div>
</template>

<script setup lang="ts">
// 网络管理页面 - 开发中
</script>

<style scoped>
.networks-page {
  padding: 24px;
  max-width: 1400px;
  margin: 0 auto;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 32px;
  padding-bottom: 24px;
  border-bottom: 1px solid var(--border-color);
}

.header-content {
  flex: 1;
}

.page-title {
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 32px;
  font-weight: 700;
  color: var(--text-primary);
  margin: 0 0 8px 0;
}

.title-icon {
  font-size: 28px;
}

.page-description {
  font-size: 16px;
  color: var(--text-secondary);
  margin: 0;
}

.coming-soon {
  text-align: center;
  padding: 80px 20px;
}

.coming-soon-icon {
  font-size: 64px;
  margin-bottom: 24px;
}

.coming-soon h2 {
  font-size: 24px;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0 0 16px 0;
}

.coming-soon p {
  font-size: 16px;
  color: var(--text-secondary);
  margin: 0;
}
</style>
