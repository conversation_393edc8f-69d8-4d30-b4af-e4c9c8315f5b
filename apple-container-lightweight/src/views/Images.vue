<template>
  <div class="images-page">
    <div class="page-header">
      <div class="header-content">
        <h1 class="page-title">
          <span class="title-icon">💿</span>
          镜像仓库
        </h1>
        <p class="page-description">管理Docker镜像和构建</p>
      </div>
      <div class="header-actions">
        <button class="action-button" @click="refreshData">
          <span class="button-icon">🔄</span>
          刷新
        </button>
        <button class="action-button primary" @click="pullImage">
          <span class="button-icon">⬇️</span>
          拉取镜像
        </button>
      </div>
    </div>

    <div class="images-grid" v-if="!loading">
      <div v-for="image in imageList" :key="image.id" class="image-card">
        <div class="image-icon">💿</div>
        <div class="image-info">
          <h3 class="image-name">{{ image.name }}</h3>
          <p class="image-tag">{{ image.tag }}</p>
          <p class="image-arch">{{ image.architecture }}</p>
        </div>
        <div class="image-actions">
          <button class="action-btn danger" @click="removeImage(image)" title="删除镜像">
            🗑️
          </button>
        </div>
      </div>
    </div>

    <div v-if="loading" class="loading-state">
      <div class="spinner">⚡</div>
      <p>加载镜像列表...</p>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { getImageList, removeImage as removeImageAPI } from '../api/container'
import type { AppleImage } from '../api/container'

const loading = ref(false)
const imageList = ref<AppleImage[]>([])

const loadData = async () => {
  loading.value = true
  try {
    const images = await getImageList()
    imageList.value = images
  } catch (error) {
    console.error('加载镜像列表失败:', error)
  } finally {
    loading.value = false
  }
}

const refreshData = () => {
  loadData()
}

const pullImage = () => {
  alert('拉取镜像功能开发中...')
}

const removeImage = async (image: AppleImage) => {
  if (confirm(`确定要删除镜像 ${image.name}:${image.tag} 吗？`)) {
    try {
      const success = await removeImageAPI(image.id)
      if (success) {
        await loadData()
      } else {
        alert('删除镜像失败')
      }
    } catch (error) {
      console.error('删除镜像失败:', error)
      alert('删除镜像失败')
    }
  }
}

onMounted(() => {
  loadData()
})
</script>

<style scoped>
.images-page {
  padding: 24px;
  max-width: 1400px;
  margin: 0 auto;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 32px;
  padding-bottom: 24px;
  border-bottom: 1px solid var(--border-color);
}

.header-content {
  flex: 1;
}

.page-title {
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 32px;
  font-weight: 700;
  color: var(--text-primary);
  margin: 0 0 8px 0;
}

.title-icon {
  font-size: 28px;
}

.page-description {
  font-size: 16px;
  color: var(--text-secondary);
  margin: 0;
}

.header-actions {
  display: flex;
  gap: 12px;
}

.action-button {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 20px;
  background: var(--glass-bg);
  backdrop-filter: blur(20px);
  border: 1px solid var(--border-color);
  border-radius: 12px;
  color: var(--text-primary);
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.action-button:hover {
  background: var(--bg-tertiary);
  transform: translateY(-2px);
  box-shadow: 0 8px 24px var(--shadow-light);
}

.action-button.primary {
  background: var(--accent-blue);
  color: white;
  border: none;
}

.action-button.primary:hover {
  background: #0056cc;
}

.button-icon {
  font-size: 16px;
}

.images-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 24px;
}

.image-card {
  background: var(--glass-bg);
  backdrop-filter: blur(20px);
  border: 1px solid var(--border-color);
  border-radius: 16px;
  padding: 24px;
  text-align: center;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
}

.image-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 16px 48px var(--shadow-medium);
}

.image-icon {
  font-size: 48px;
  margin-bottom: 16px;
}

.image-name {
  font-size: 18px;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0 0 8px 0;
}

.image-tag {
  font-size: 14px;
  color: var(--accent-blue);
  margin: 0 0 4px 0;
  font-weight: 500;
}

.image-arch {
  font-size: 12px;
  color: var(--text-secondary);
  margin: 0;
  font-family: 'SF Mono', Monaco, monospace;
}

.image-actions {
  position: absolute;
  top: 16px;
  right: 16px;
}

.action-btn {
  width: 32px;
  height: 32px;
  border: none;
  border-radius: 8px;
  background: var(--bg-tertiary);
  color: var(--text-secondary);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  transition: all 0.2s ease;
}

.action-btn:hover {
  background: var(--border-color);
  color: var(--text-primary);
  transform: scale(1.05);
}

.action-btn.danger {
  background: var(--accent-red);
  color: white;
}

.loading-state {
  text-align: center;
  padding: 80px 20px;
}

.spinner {
  font-size: 48px;
  margin-bottom: 16px;
  display: inline-block;
  animation: pulse 2s infinite;
}

.loading-state p {
  font-size: 16px;
  color: var(--text-secondary);
  margin: 0;
}
</style>
