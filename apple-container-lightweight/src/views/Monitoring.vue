<template>
  <div class="monitoring-page">
    <div class="page-header">
      <div class="header-content">
        <h1 class="page-title">
          <span class="title-icon">📈</span>
          性能监控
        </h1>
        <p class="page-description">实时性能监控和资源使用情况</p>
      </div>
      <div class="header-actions">
        <div class="status-indicator" :class="{ active: isMonitoring }">
          <span class="indicator-dot"></span>
          <span class="indicator-text">{{ isMonitoring ? '实时监控中' : '监控已停止' }}</span>
        </div>
        <button class="action-button" @click="toggleMonitoring">
          <span class="button-icon">{{ isMonitoring ? '⏸️' : '▶️' }}</span>
          {{ isMonitoring ? '暂停监控' : '开始监控' }}
        </button>
      </div>
    </div>

    <!-- 实时指标卡片 -->
    <div class="metrics-grid">
      <div class="metric-card cpu">
        <div class="metric-header">
          <div class="metric-icon">🔥</div>
          <div class="metric-info">
            <h3>CPU 使用率</h3>
            <p class="metric-value">{{ currentMetrics.cpu }}%</p>
          </div>
        </div>
        <div class="metric-trend" :class="cpuTrend">
          <span class="trend-icon">{{ cpuTrend === 'up' ? '↗️' : cpuTrend === 'down' ? '↘️' : '➡️' }}</span>
          <span class="trend-text">{{ cpuTrendText }}</span>
        </div>
      </div>

      <div class="metric-card memory">
        <div class="metric-header">
          <div class="metric-icon">💾</div>
          <div class="metric-info">
            <h3>内存使用率</h3>
            <p class="metric-value">{{ currentMetrics.memory }}%</p>
          </div>
        </div>
        <div class="metric-trend" :class="memoryTrend">
          <span class="trend-icon">{{ memoryTrend === 'up' ? '↗️' : memoryTrend === 'down' ? '↘️' : '➡️' }}</span>
          <span class="trend-text">{{ memoryTrendText }}</span>
        </div>
      </div>

      <div class="metric-card network">
        <div class="metric-header">
          <div class="metric-icon">🌐</div>
          <div class="metric-info">
            <h3>网络流量</h3>
            <p class="metric-value">{{ currentMetrics.network }} MB/s</p>
          </div>
        </div>
        <div class="metric-trend" :class="networkTrend">
          <span class="trend-icon">{{ networkTrend === 'up' ? '↗️' : networkTrend === 'down' ? '↘️' : '➡️' }}</span>
          <span class="trend-text">{{ networkTrendText }}</span>
        </div>
      </div>

      <div class="metric-card containers">
        <div class="metric-header">
          <div class="metric-icon">📦</div>
          <div class="metric-info">
            <h3>运行容器</h3>
            <p class="metric-value">{{ currentMetrics.containers }}</p>
          </div>
        </div>
        <div class="metric-trend stable">
          <span class="trend-icon">✅</span>
          <span class="trend-text">运行正常</span>
        </div>
      </div>
    </div>

    <!-- 性能图表 -->
    <div class="charts-grid">
      <PerformanceChart
        title="CPU 使用率趋势"
        type="cpu"
        class="chart-item"
      />
      <PerformanceChart
        title="内存使用率趋势"
        type="memory"
        class="chart-item"
      />
      <PerformanceChart
        title="网络流量趋势"
        type="network"
        class="chart-item"
      />
      <PerformanceChart
        title="容器数量变化"
        type="containers"
        class="chart-item"
      />
    </div>

    <!-- 系统资源详情 -->
    <div class="resource-details">
      <div class="section-header">
        <h2 class="section-title">系统资源详情</h2>
      </div>

      <div class="details-grid">
        <div class="detail-card">
          <h4>处理器信息</h4>
          <div class="detail-content">
            <div class="detail-item">
              <span class="label">型号:</span>
              <span class="value">Apple M4</span>
            </div>
            <div class="detail-item">
              <span class="label">核心数:</span>
              <span class="value">10核 (4性能 + 6效率)</span>
            </div>
            <div class="detail-item">
              <span class="label">频率:</span>
              <span class="value">最高 4.4 GHz</span>
            </div>
            <div class="detail-item">
              <span class="label">温度:</span>
              <span class="value">{{ systemTemp }}°C</span>
            </div>
          </div>
        </div>

        <div class="detail-card">
          <h4>内存信息</h4>
          <div class="detail-content">
            <div class="detail-item">
              <span class="label">总容量:</span>
              <span class="value">24 GB</span>
            </div>
            <div class="detail-item">
              <span class="label">已使用:</span>
              <span class="value">{{ memoryUsed }} GB</span>
            </div>
            <div class="detail-item">
              <span class="label">可用:</span>
              <span class="value">{{ memoryAvailable }} GB</span>
            </div>
            <div class="detail-item">
              <span class="label">类型:</span>
              <span class="value">LPDDR5</span>
            </div>
          </div>
        </div>

        <div class="detail-card">
          <h4>存储信息</h4>
          <div class="detail-content">
            <div class="detail-item">
              <span class="label">总容量:</span>
              <span class="value">1 TB SSD</span>
            </div>
            <div class="detail-item">
              <span class="label">已使用:</span>
              <span class="value">{{ storageUsed }} GB</span>
            </div>
            <div class="detail-item">
              <span class="label">可用:</span>
              <span class="value">{{ storageAvailable }} GB</span>
            </div>
            <div class="detail-item">
              <span class="label">读写速度:</span>
              <span class="value">7.4 GB/s</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import PerformanceChart from '../components/PerformanceChart.vue'

const isMonitoring = ref(true)
const currentMetrics = ref({
  cpu: 25,
  memory: 45,
  network: 2.4,
  containers: 2
})

const previousMetrics = ref({
  cpu: 23,
  memory: 42,
  network: 2.1,
  containers: 2
})

const systemTemp = ref(42)
const memoryUsed = ref(10.8)
const memoryAvailable = ref(13.2)
const storageUsed = ref(456)
const storageAvailable = ref(568)

// 计算趋势
const cpuTrend = computed(() => {
  const diff = currentMetrics.value.cpu - previousMetrics.value.cpu
  return diff > 1 ? 'up' : diff < -1 ? 'down' : 'stable'
})

const memoryTrend = computed(() => {
  const diff = currentMetrics.value.memory - previousMetrics.value.memory
  return diff > 1 ? 'up' : diff < -1 ? 'down' : 'stable'
})

const networkTrend = computed(() => {
  const diff = currentMetrics.value.network - previousMetrics.value.network
  return diff > 0.2 ? 'up' : diff < -0.2 ? 'down' : 'stable'
})

const cpuTrendText = computed(() => {
  const diff = Math.abs(currentMetrics.value.cpu - previousMetrics.value.cpu)
  return cpuTrend.value === 'stable' ? '稳定' : `${diff.toFixed(1)}%`
})

const memoryTrendText = computed(() => {
  const diff = Math.abs(currentMetrics.value.memory - previousMetrics.value.memory)
  return memoryTrend.value === 'stable' ? '稳定' : `${diff.toFixed(1)}%`
})

const networkTrendText = computed(() => {
  const diff = Math.abs(currentMetrics.value.network - previousMetrics.value.network)
  return networkTrend.value === 'stable' ? '稳定' : `${diff.toFixed(1)} MB/s`
})

const toggleMonitoring = () => {
  isMonitoring.value = !isMonitoring.value
}

// 模拟实时数据更新
let updateInterval: NodeJS.Timeout | null = null

const updateMetrics = () => {
  if (!isMonitoring.value) return

  // 保存之前的数据
  previousMetrics.value = { ...currentMetrics.value }

  // 生成新的模拟数据
  currentMetrics.value = {
    cpu: Math.max(5, Math.min(95, currentMetrics.value.cpu + (Math.random() - 0.5) * 10)),
    memory: Math.max(10, Math.min(90, currentMetrics.value.memory + (Math.random() - 0.5) * 8)),
    network: Math.max(0.1, Math.min(10, currentMetrics.value.network + (Math.random() - 0.5) * 2)),
    containers: Math.max(1, Math.min(5, currentMetrics.value.containers + Math.floor((Math.random() - 0.8) * 2)))
  }

  // 更新其他系统信息
  systemTemp.value = Math.max(35, Math.min(65, systemTemp.value + (Math.random() - 0.5) * 4))
  memoryUsed.value = (currentMetrics.value.memory / 100) * 24
  memoryAvailable.value = 24 - memoryUsed.value
}

onMounted(() => {
  // 每3秒更新一次数据
  updateInterval = setInterval(updateMetrics, 3000)
})

onUnmounted(() => {
  if (updateInterval) {
    clearInterval(updateInterval)
  }
})
</script>

<style scoped>
.monitoring-page {
  padding: 24px;
  max-width: 1400px;
  margin: 0 auto;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 32px;
  padding-bottom: 24px;
  border-bottom: 1px solid var(--border-color);
  flex-wrap: wrap;
  gap: 16px;
}

.header-content {
  flex: 1;
  min-width: 200px;
}

.page-title {
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 32px;
  font-weight: 700;
  color: var(--text-primary);
  margin: 0 0 8px 0;
}

.title-icon {
  font-size: 28px;
}

.page-description {
  font-size: 16px;
  color: var(--text-secondary);
  margin: 0;
}

.header-actions {
  display: flex;
  gap: 16px;
  align-items: center;
  flex-wrap: wrap;
}

.status-indicator {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  border-radius: 8px;
  background: var(--bg-tertiary);
  font-size: 14px;
  color: var(--text-secondary);
  transition: all 0.2s ease;
}

.status-indicator.active {
  background: rgba(52, 199, 89, 0.1);
  color: var(--accent-green);
}

.indicator-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: currentColor;
}

.status-indicator.active .indicator-dot {
  animation: pulse 2s infinite;
}

.action-button {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 20px;
  background: var(--glass-bg);
  backdrop-filter: blur(20px);
  border: 1px solid var(--border-color);
  border-radius: 12px;
  color: var(--text-primary);
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.action-button:hover {
  background: var(--bg-tertiary);
  transform: translateY(-2px);
  box-shadow: 0 8px 24px var(--shadow-light);
}

.button-icon {
  font-size: 16px;
}

/* 指标卡片 */
.metrics-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 24px;
  margin-bottom: 40px;
}

.metric-card {
  background: var(--glass-bg);
  backdrop-filter: blur(20px);
  border: 1px solid var(--border-color);
  border-radius: 16px;
  padding: 24px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.metric-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 16px 48px var(--shadow-medium);
}

.metric-card.cpu {
  border-left: 4px solid #ff6b6b;
}

.metric-card.memory {
  border-left: 4px solid var(--accent-blue);
}

.metric-card.network {
  border-left: 4px solid var(--accent-green);
}

.metric-card.containers {
  border-left: 4px solid var(--accent-orange);
}

.metric-header {
  display: flex;
  align-items: center;
  gap: 16px;
  margin-bottom: 16px;
}

.metric-icon {
  font-size: 32px;
  width: 48px;
  height: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 12px;
  background: var(--bg-tertiary);
}

.metric-info h3 {
  font-size: 16px;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0 0 4px 0;
}

.metric-value {
  font-size: 28px;
  font-weight: 700;
  color: var(--text-primary);
  margin: 0;
  font-family: 'SF Mono', Monaco, monospace;
}

.metric-trend {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 12px;
  font-weight: 600;
  padding: 4px 8px;
  border-radius: 6px;
  position: absolute;
  top: 16px;
  right: 16px;
}

.metric-trend.up {
  background: rgba(52, 199, 89, 0.1);
  color: var(--accent-green);
}

.metric-trend.down {
  background: rgba(255, 59, 48, 0.1);
  color: var(--accent-red);
}

.metric-trend.stable {
  background: rgba(142, 142, 147, 0.1);
  color: var(--text-secondary);
}

.trend-icon {
  font-size: 14px;
}

/* 图表网格 */
.charts-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(500px, 1fr));
  gap: 24px;
  margin-bottom: 40px;
}

.chart-item {
  min-height: 400px;
}

/* 资源详情 */
.resource-details {
  margin-bottom: 40px;
}

.section-header {
  margin-bottom: 24px;
}

.section-title {
  font-size: 24px;
  font-weight: 700;
  color: var(--text-primary);
  margin: 0;
}

.details-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 24px;
}

.detail-card {
  background: var(--glass-bg);
  backdrop-filter: blur(20px);
  border: 1px solid var(--border-color);
  border-radius: 16px;
  padding: 24px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.detail-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 12px 32px var(--shadow-light);
}

.detail-card h4 {
  font-size: 18px;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0 0 16px 0;
}

.detail-content {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.detail-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 14px;
}

.detail-item .label {
  color: var(--text-secondary);
  font-weight: 500;
}

.detail-item .value {
  color: var(--text-primary);
  font-weight: 600;
  font-family: 'SF Mono', Monaco, monospace;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .charts-grid {
    grid-template-columns: 1fr;
  }

  .chart-item {
    min-height: 300px;
  }

  .metrics-grid {
    grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
  }

  .page-header {
    flex-direction: column;
    align-items: stretch;
  }

  .header-actions {
    justify-content: stretch;
  }

  .action-button {
    flex: 1;
    justify-content: center;
  }
}

/* 动画效果 */
@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}
</style>
