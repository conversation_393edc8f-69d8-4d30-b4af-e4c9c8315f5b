<template>
  <div class="intelligence-center">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <h1 class="page-title">
          <AppleIcon name="intelligence" size="xlarge" variant="fill" class="title-icon" />
          Apple Intelligence Center
        </h1>
        <p class="page-description">集成化的应用和资源中心，专为苹果生态系统优化</p>
      </div>
      <div class="header-actions">
        <div class="search-container">
          <AppleIcon name="search" size="medium" class="search-icon" />
          <input 
            v-model="searchQuery" 
            type="text" 
            placeholder="搜索镜像、工具、教程..." 
            class="search-input"
            @input="handleSearch"
          />
          <div v-if="searchQuery" class="search-clear" @click="clearSearch">
            <AppleIcon name="x" size="small" />
          </div>
        </div>
        <button class="filter-button" @click="toggleFilters" :class="{ active: showFilters }">
          <AppleIcon name="filter" size="medium" />
          筛选
        </button>
      </div>
    </div>

    <!-- 快速导航 -->
    <div class="quick-nav">
      <div class="nav-tabs">
        <button 
          v-for="tab in tabs" 
          :key="tab.id"
          class="nav-tab"
          :class="{ active: activeTab === tab.id }"
          @click="setActiveTab(tab.id)"
        >
          <AppleIcon :name="tab.icon" size="medium" />
          <span>{{ tab.name }}</span>
          <span class="tab-count">{{ tab.count }}</span>
        </button>
      </div>
    </div>

    <!-- 筛选面板 -->
    <div v-if="showFilters" class="filters-panel">
      <div class="filter-section">
        <h3>分类</h3>
        <div class="filter-tags">
          <button 
            v-for="category in categories" 
            :key="category.id"
            class="filter-tag"
            :class="{ active: selectedCategories.includes(category.id) }"
            @click="toggleCategory(category.id)"
          >
            {{ category.name }}
          </button>
        </div>
      </div>
      <div class="filter-section">
        <h3>架构</h3>
        <div class="filter-tags">
          <button 
            v-for="arch in architectures" 
            :key="arch"
            class="filter-tag"
            :class="{ active: selectedArchitectures.includes(arch) }"
            @click="toggleArchitecture(arch)"
          >
            {{ arch }}
          </button>
        </div>
      </div>
    </div>

    <!-- 主内容区域 -->
    <div class="main-content">
      <!-- 侧边栏 -->
      <div class="sidebar">
        <div class="sidebar-section">
          <h3>收藏夹</h3>
          <div class="favorites-list">
            <div v-for="item in favorites" :key="item.id" class="favorite-item">
              <AppleIcon :name="item.icon" size="small" />
              <span>{{ item.name }}</span>
            </div>
          </div>
        </div>
        
        <!-- 乔布斯纪念区域 -->
        <div class="sidebar-section tribute-section">
          <SteveJobsPortrait />
          <SteveJobsQuote compact />
        </div>

        <!-- 音乐播放器 -->
        <div class="sidebar-section music-section">
          <AppleMusicPlayer mini />
        </div>

        <div class="sidebar-section">
          <h3>最近使用</h3>
          <div class="recent-list">
            <div v-for="item in recentItems" :key="item.id" class="recent-item">
              <AppleIcon :name="item.icon" size="small" />
              <span>{{ item.name }}</span>
              <span class="recent-time">{{ item.time }}</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 内容区域 -->
      <div class="content-area">
        <!-- 镜像仓库中心 -->
        <div v-if="activeTab === 'images'" class="tab-content">
          <ImageRepository />
        </div>

        <!-- 资讯聚合中心 -->
        <div v-if="activeTab === 'news'" class="tab-content">
          <NewsCenter />
        </div>

        <!-- 教育资源板块 -->
        <div v-if="activeTab === 'education'" class="tab-content">
          <EducationCenter />
        </div>

        <!-- 社区分享功能 -->
        <div v-if="activeTab === 'community'" class="tab-content">
          <CommunityCenter />
        </div>

        <!-- Web应用工具箱 -->
        <div v-if="activeTab === 'tools'" class="tab-content">
          <ToolboxCenter />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import AppleIcon from '../components/AppleIcon.vue'
import ImageRepository from '../components/intelligence/ImageRepository.vue'
import NewsCenter from '../components/intelligence/NewsCenter.vue'
import EducationCenter from '../components/intelligence/EducationCenter.vue'
import CommunityCenter from '../components/intelligence/CommunityCenter.vue'
import ToolboxCenter from '../components/intelligence/ToolboxCenter.vue'
import SteveJobsPortrait from '../components/tribute/SteveJobsPortrait.vue'
import SteveJobsQuote from '../components/tribute/SteveJobsQuote.vue'
import AppleMusicPlayer from '../components/music/AppleMusicPlayer.vue'
import { useIntelligenceStore } from '../stores/intelligence'

// 使用状态管理
const intelligenceStore = useIntelligenceStore()

// 响应式数据
const searchQuery = ref('')
const showFilters = ref(false)
const activeTab = ref('images')
const selectedCategories = ref<string[]>([])
const selectedArchitectures = ref<string[]>([])

// 标签页配置
const tabs = ref([
  { id: 'images', name: 'Image Hub', icon: 'images', count: 1247, subtitle: 'Apple 最好用的镜像资源平台' },
  { id: 'news', name: '资讯中心', icon: 'news', count: 156 },
  { id: 'education', name: '教育资源', icon: 'graduation', count: 89 },
  { id: 'community', name: '社区分享', icon: 'community', count: 234 },
  { id: 'tools', name: '工具箱', icon: 'toolbox', count: 67 }
])

// 分类配置
const categories = ref([
  { id: 'ai-ml', name: 'AI/ML' },
  { id: 'dev-tools', name: '开发工具' },
  { id: 'databases', name: '数据库' },
  { id: 'web-services', name: 'Web服务' },
  { id: 'monitoring', name: '监控工具' },
  { id: 'security', name: '安全工具' }
])

// 架构选项
const architectures = ref(['ARM64', 'x86_64', 'Universal'])

// 收藏夹
const favorites = ref([
  { id: 1, name: 'TensorFlow', icon: 'brain' },
  { id: 2, name: 'PostgreSQL', icon: 'database' },
  { id: 3, name: 'Nginx', icon: 'server' }
])

// 最近使用
const recentItems = ref([
  { id: 1, name: 'Docker Hub', icon: 'containers', time: '2分钟前' },
  { id: 2, name: 'Hugging Face', icon: 'brain', time: '10分钟前' },
  { id: 3, name: 'GitHub Registry', icon: 'github', time: '1小时前' }
])

// 方法
const handleSearch = () => {
  // 实现搜索逻辑
  console.log('搜索:', searchQuery.value)
}

const clearSearch = () => {
  searchQuery.value = ''
}

const toggleFilters = () => {
  showFilters.value = !showFilters.value
}

const setActiveTab = (tabId: string) => {
  activeTab.value = tabId
}

const toggleCategory = (categoryId: string) => {
  const index = selectedCategories.value.indexOf(categoryId)
  if (index > -1) {
    selectedCategories.value.splice(index, 1)
  } else {
    selectedCategories.value.push(categoryId)
  }
}

const toggleArchitecture = (arch: string) => {
  const index = selectedArchitectures.value.indexOf(arch)
  if (index > -1) {
    selectedArchitectures.value.splice(index, 1)
  } else {
    selectedArchitectures.value.push(arch)
  }
}

// 生命周期
onMounted(async () => {
  console.log('Intelligence Center 已加载')
  // 初始化数据
  await intelligenceStore.initializeData()
})

onUnmounted(() => {
  // 清理数据
  intelligenceStore.clearData()
})
</script>

<style scoped>
.intelligence-center {
  padding: 0;
  max-width: 1600px;
  margin: 0 auto;
  background: transparent;
  animation: fadeIn 0.6s ease-out;
}

/* 页面头部 */
.page-header {
  padding: 32px 24px 24px 24px;
  margin-top: 24px;
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 32px;
  padding-bottom: 24px;
  border-bottom: 1px solid var(--border-color);
  flex-wrap: wrap;
  gap: 16px;
}

.header-content {
  flex: 1;
  min-width: 300px;
}

.page-title {
  display: flex;
  align-items: center;
  gap: 16px;
  font-size: 32px;
  font-weight: 700;
  color: var(--text-primary);
  margin: 0 0 8px 0;
}

.title-icon {
  color: var(--accent-blue);
  filter: drop-shadow(0 2px 4px rgba(0, 122, 255, 0.3));
}

.page-description {
  font-size: 16px;
  color: var(--text-secondary);
  margin: 0;
}

.header-actions {
  display: flex;
  gap: 16px;
  align-items: center;
  flex-wrap: wrap;
}

/* 搜索容器 */
.search-container {
  position: relative;
  display: flex;
  align-items: center;
  background: var(--gradient-card);
  backdrop-filter: blur(20px);
  border: 1px solid var(--border-color);
  border-radius: 12px;
  padding: 0 16px;
  min-width: 300px;
  transition: all 0.2s ease;
}

.search-container:focus-within {
  border-color: var(--accent-blue);
  box-shadow: 0 0 0 3px rgba(0, 122, 255, 0.1);
}

.search-icon {
  color: var(--text-tertiary);
  margin-right: 12px;
}

.search-input {
  flex: 1;
  border: none;
  background: transparent;
  padding: 12px 0;
  font-size: 14px;
  color: var(--text-primary);
  outline: none;
}

.search-input::placeholder {
  color: var(--text-tertiary);
}

.search-clear {
  cursor: pointer;
  color: var(--text-tertiary);
  padding: 4px;
  border-radius: 4px;
  transition: all 0.2s ease;
}

.search-clear:hover {
  color: var(--text-secondary);
  background: var(--bg-tertiary);
}

.filter-button {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 16px;
  background: var(--gradient-card);
  backdrop-filter: blur(20px);
  border: 1px solid var(--border-color);
  border-radius: 12px;
  color: var(--text-primary);
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.filter-button:hover {
  background: var(--bg-tertiary);
  transform: translateY(-2px);
  box-shadow: 0 8px 24px var(--shadow-light);
}

.filter-button.active {
  background: var(--accent-blue);
  color: white;
  border-color: var(--accent-blue);
}

/* 快速导航 */
.quick-nav {
  margin-bottom: 24px;
}

.nav-tabs {
  display: flex;
  gap: 8px;
  overflow-x: auto;
  padding-bottom: 8px;
}

.nav-tab {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 20px;
  background: var(--gradient-card);
  backdrop-filter: blur(20px);
  border: 1px solid var(--border-color);
  border-radius: 12px;
  color: var(--text-secondary);
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  white-space: nowrap;
}

.nav-tab:hover {
  color: var(--text-primary);
  transform: translateY(-2px);
  box-shadow: 0 8px 24px var(--shadow-light);
}

.nav-tab.active {
  background: var(--accent-blue);
  color: white;
  border-color: var(--accent-blue);
}

.tab-count {
  background: rgba(255, 255, 255, 0.2);
  padding: 2px 8px;
  border-radius: 10px;
  font-size: 12px;
  font-weight: 600;
}

.nav-tab.active .tab-count {
  background: rgba(255, 255, 255, 0.3);
}

/* 筛选面板 */
.filters-panel {
  background: var(--gradient-card);
  backdrop-filter: blur(20px);
  border: 1px solid var(--border-color);
  border-radius: 16px;
  padding: 24px;
  margin-bottom: 24px;
  animation: slideDown 0.3s ease-out;
}

.filter-section {
  margin-bottom: 20px;
}

.filter-section:last-child {
  margin-bottom: 0;
}

.filter-section h3 {
  font-size: 16px;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0 0 12px 0;
}

.filter-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.filter-tag {
  padding: 6px 12px;
  background: var(--bg-tertiary);
  border: 1px solid var(--border-color);
  border-radius: 8px;
  color: var(--text-secondary);
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.filter-tag:hover {
  color: var(--text-primary);
  background: var(--bg-secondary);
}

.filter-tag.active {
  background: var(--accent-blue);
  color: white;
  border-color: var(--accent-blue);
}

/* 主内容区域 */
.main-content {
  display: grid;
  grid-template-columns: 280px 1fr;
  gap: 24px;
}

/* 侧边栏 */
.sidebar {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.sidebar-section {
  background: var(--gradient-card);
  backdrop-filter: blur(20px);
  border: 1px solid var(--border-color);
  border-radius: 16px;
  padding: 20px;
}

.sidebar-section h3 {
  font-size: 16px;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0 0 16px 0;
}

/* 乔布斯纪念区域样式 */
.tribute-section {
  background: linear-gradient(135deg,
    rgba(255, 255, 255, 0.08) 0%,
    rgba(255, 255, 255, 0.02) 100%) !important;
  border: 1px solid rgba(255, 255, 255, 0.15) !important;
  transition: all 0.3s ease;
}

.tribute-section:hover {
  background: linear-gradient(135deg,
    rgba(255, 255, 255, 0.12) 0%,
    rgba(255, 255, 255, 0.04) 100%) !important;
  transform: translateY(-2px);
  box-shadow:
    0 12px 40px rgba(0, 0, 0, 0.15),
    inset 0 1px 0 rgba(255, 255, 255, 0.15);
}

/* 音乐播放器区域样式 */
.music-section {
  display: flex;
  justify-content: center;
  padding: 16px !important;
}

.music-section .apple-music-player {
  width: 100%;
  max-width: none;
}

.favorites-list,
.recent-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.favorite-item,
.recent-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 14px;
  color: var(--text-secondary);
}

.favorite-item:hover,
.recent-item:hover {
  background: var(--bg-tertiary);
  color: var(--text-primary);
}

.recent-time {
  margin-left: auto;
  font-size: 12px;
  color: var(--text-tertiary);
}

/* 内容区域 */
.content-area {
  min-height: 600px;
}

.tab-content {
  animation: fadeIn 0.3s ease-out;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .main-content {
    grid-template-columns: 1fr;
  }
  
  .sidebar {
    order: 2;
  }
}

@media (max-width: 768px) {
  .intelligence-center {
    padding: 0;
  }

  .page-header {
    flex-direction: column;
    align-items: stretch;
    padding: 24px 20px 16px 20px;
    margin-top: 16px;
  }

  .header-actions {
    flex-direction: column;
  }

  .search-container {
    min-width: auto;
  }

  .nav-tabs {
    flex-wrap: wrap;
    padding: 0 20px;
  }

  .main-content {
    padding: 0 20px;
  }
}

/* 动画效果 */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
</style>
