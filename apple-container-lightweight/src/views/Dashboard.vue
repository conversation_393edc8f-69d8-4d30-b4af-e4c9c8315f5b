<template>
  <div class="dashboard">
    <!-- 页面标题 -->
    <div class="page-header">
      <div class="header-content">
        <h1 class="page-title">
          <AppleIcon name="dashboard" size="xlarge" variant="fill" class="title-icon" />
          仪表板
        </h1>
        <p class="page-description">系统概览和关键性能指标</p>
      </div>
      <div class="header-actions">
        <button class="action-button" @click="refreshData">
          <AppleIcon name="refresh" size="medium" class="button-icon" />
          刷新数据
        </button>
      </div>
    </div>

    <!-- 系统状态卡片 -->
    <div class="stats-grid">
      <div class="stat-card">
        <div class="stat-header">
          <div class="stat-icon running">
            <AppleIcon name="containers" size="large" variant="fill" />
          </div>
          <div class="stat-trend up">
            <AppleIcon name="chevron-up" size="small" />
            <span>+2</span>
          </div>
        </div>
        <div class="stat-content">
          <h3 class="stat-value">{{ systemStats.runningContainers }}</h3>
          <p class="stat-label">运行中容器</p>
          <div class="stat-detail">
            <span>总计 {{ systemStats.totalContainers }} 个</span>
          </div>
        </div>
      </div>

      <div class="stat-card">
        <div class="stat-header">
          <div class="stat-icon images">
            <span>💿</span>
          </div>
          <div class="stat-trend stable">
            <span>➡️</span>
            <span>0</span>
          </div>
        </div>
        <div class="stat-content">
          <h3 class="stat-value">{{ systemStats.totalImages }}</h3>
          <p class="stat-label">可用镜像</p>
          <div class="stat-detail">
            <span>ARM64 架构</span>
          </div>
        </div>
      </div>

      <div class="stat-card">
        <div class="stat-header">
          <div class="stat-icon cpu">
            <span>🔥</span>
          </div>
          <div class="stat-trend down">
            <span>↘️</span>
            <span>-5%</span>
          </div>
        </div>
        <div class="stat-content">
          <h3 class="stat-value">{{ systemStats.cpuUsage }}%</h3>
          <p class="stat-label">CPU 使用率</p>
          <div class="stat-detail">
            <span>Apple M4</span>
          </div>
        </div>
      </div>

      <div class="stat-card">
        <div class="stat-header">
          <div class="stat-icon memory">
            <span>💾</span>
          </div>
          <div class="stat-trend up">
            <span>↗️</span>
            <span>+1.2GB</span>
          </div>
        </div>
        <div class="stat-content">
          <h3 class="stat-value">{{ systemStats.memoryUsage }}%</h3>
          <p class="stat-label">内存使用率</p>
          <div class="stat-detail">
            <span>{{ systemStats.memoryUsed }}/{{ systemStats.memoryTotal }}</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 容器状态概览 -->
    <div class="overview-section">
      <div class="section-header">
        <h2 class="section-title">容器状态概览</h2>
        <div class="section-actions">
          <button class="view-all-button" @click="$router.push('/containers')">
            查看全部
            <span>→</span>
          </button>
        </div>
      </div>
      
      <div class="container-overview">
        <div class="container-list">
          <div 
            v-for="container in recentContainers" 
            :key="container.id"
            class="container-item"
          >
            <div class="container-status" :class="container.state">
              <div class="status-dot"></div>
            </div>
            <div class="container-info">
              <h4 class="container-name">{{ container.name }}</h4>
              <p class="container-image">{{ container.image }}</p>
            </div>
            <div class="container-meta">
              <span class="container-arch">{{ container.arch }}</span>
              <span class="container-ip">{{ container.addr }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 实时性能监控 -->
    <div class="performance-section">
      <div class="section-header">
        <h2 class="section-title">实时性能监控</h2>
        <div class="section-actions">
          <div class="refresh-indicator" :class="{ active: isRefreshing }">
            <span class="indicator-dot"></span>
            <span class="indicator-text">{{ isRefreshing ? '更新中...' : '实时监控' }}</span>
          </div>
        </div>
      </div>

      <div class="performance-grid">
        <div class="performance-card">
          <div class="performance-header">
            <h4>CPU 使用率</h4>
            <span class="performance-value">{{ systemStats.cpuUsage }}%</span>
          </div>
          <div class="performance-chart">
            <div class="progress-bar">
              <div
                class="progress-fill cpu"
                :style="{ width: systemStats.cpuUsage + '%' }"
              ></div>
            </div>
          </div>
        </div>

        <div class="performance-card">
          <div class="performance-header">
            <h4>内存使用率</h4>
            <span class="performance-value">{{ systemStats.memoryUsage }}%</span>
          </div>
          <div class="performance-chart">
            <div class="progress-bar">
              <div
                class="progress-fill memory"
                :style="{ width: systemStats.memoryUsage + '%' }"
              ></div>
            </div>
          </div>
        </div>

        <div class="performance-card">
          <div class="performance-header">
            <h4>网络流量</h4>
            <span class="performance-value">{{ networkTraffic }} MB/s</span>
          </div>
          <div class="performance-chart">
            <div class="network-indicator">
              <span class="upload">↑ {{ uploadSpeed }} MB/s</span>
              <span class="download">↓ {{ downloadSpeed }} MB/s</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 系统信息 -->
    <div class="system-info-section">
      <div class="section-header">
        <h2 class="section-title">系统信息</h2>
      </div>

      <div class="system-info-grid">
        <div class="info-card">
          <div class="info-header">
            <span class="info-icon">🍎</span>
            <h3>硬件信息</h3>
          </div>
          <div class="info-content">
            <div class="info-item">
              <span class="label">处理器:</span>
              <span class="value">Apple M4</span>
            </div>
            <div class="info-item">
              <span class="label">内存:</span>
              <span class="value">24 GB</span>
            </div>
            <div class="info-item">
              <span class="label">架构:</span>
              <span class="value">ARM64</span>
            </div>
          </div>
        </div>

        <div class="info-card">
          <div class="info-header">
            <span class="info-icon">💻</span>
            <h3>系统信息</h3>
          </div>
          <div class="info-content">
            <div class="info-item">
              <span class="label">系统:</span>
              <span class="value">macOS Tahoe 26.0</span>
            </div>
            <div class="info-item">
              <span class="label">容器版本:</span>
              <span class="value">Apple Container 2025.6</span>
            </div>
            <div class="info-item">
              <span class="label">API状态:</span>
              <span class="value status-running">运行中</span>
            </div>
          </div>
        </div>

        <div class="info-card">
          <div class="info-header">
            <span class="info-icon">⚡</span>
            <h3>运行时间</h3>
          </div>
          <div class="info-content">
            <div class="info-item">
              <span class="label">系统运行:</span>
              <span class="value">{{ systemUptime }}</span>
            </div>
            <div class="info-item">
              <span class="label">容器服务:</span>
              <span class="value">{{ containerUptime }}</span>
            </div>
            <div class="info-item">
              <span class="label">最后重启:</span>
              <span class="value">{{ lastRestart }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { getContainerList, getSystemInfo } from '../api/container'
import type { AppleContainer } from '../api/container'
import AppleIcon from '../components/AppleIcon.vue'

const systemStats = ref({
  runningContainers: 0,
  totalContainers: 0,
  totalImages: 0,
  cpuUsage: 0,
  memoryUsage: 0,
  memoryUsed: '0 GB',
  memoryTotal: '24 GB'
})

const recentContainers = ref<AppleContainer[]>([])
const loading = ref(false)
const isRefreshing = ref(false)
const networkTraffic = ref('2.4')
const uploadSpeed = ref('1.2')
const downloadSpeed = ref('3.6')
const systemUptime = ref('7天 12小时')
const containerUptime = ref('3天 8小时')
const lastRestart = ref('2024-01-20 14:30')

const loadDashboardData = async () => {
  loading.value = true
  isRefreshing.value = true
  try {
    const [containers, systemInfo] = await Promise.all([
      getContainerList(),
      getSystemInfo()
    ])

    // 更新统计数据
    systemStats.value = {
      runningContainers: systemInfo.runningContainers,
      totalContainers: systemInfo.containerCount,
      totalImages: systemInfo.imageCount,
      cpuUsage: Math.floor(Math.random() * 30 + 10), // 模拟CPU使用率
      memoryUsage: Math.floor(Math.random() * 40 + 30), // 模拟内存使用率
      memoryUsed: `${Math.floor(Math.random() * 8 + 8)} GB`,
      memoryTotal: '24 GB'
    }

    // 更新网络流量数据
    networkTraffic.value = (Math.random() * 5 + 1).toFixed(1)
    uploadSpeed.value = (Math.random() * 2 + 0.5).toFixed(1)
    downloadSpeed.value = (Math.random() * 4 + 1).toFixed(1)

    // 显示最近的容器
    recentContainers.value = containers.slice(0, 4)

  } catch (error) {
    console.error('加载仪表板数据失败:', error)
  } finally {
    loading.value = false
    setTimeout(() => {
      isRefreshing.value = false
    }, 500)
  }
}

const refreshData = () => {
  loadDashboardData()
}

onMounted(() => {
  loadDashboardData()
  
  // 每30秒自动刷新数据
  setInterval(loadDashboardData, 30000)
})
</script>

<style scoped>
.dashboard {
  padding: 24px;
  max-width: 1400px;
  margin: 0 auto;
  background: transparent;
  animation: fadeIn 0.6s ease-out;
}

/* 页面标题 */
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 32px;
  padding-bottom: 24px;
  border-bottom: 1px solid var(--border-color);
}

.header-content {
  flex: 1;
}

.page-title {
  display: flex;
  align-items: center;
  gap: 16px;
  font-size: 32px;
  font-weight: 700;
  color: var(--text-primary);
  margin: 0 0 8px 0;
}

.title-icon {
  font-size: var(--icon-title);
  color: var(--accent-blue);
}

.page-description {
  font-size: 16px;
  color: var(--text-secondary);
  margin: 0;
}

.header-actions {
  display: flex;
  gap: 12px;
}

.action-button {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 20px;
  background: var(--glass-bg);
  backdrop-filter: blur(20px);
  border: 1px solid var(--border-color);
  border-radius: 12px;
  color: var(--text-primary);
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.action-button:hover {
  background: var(--bg-tertiary);
  transform: translateY(-2px);
  box-shadow: 0 8px 24px var(--shadow-light);
}

.button-icon {
  font-size: 16px;
}

/* 统计卡片网格 */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 24px;
  margin-bottom: 40px;
  animation: fadeInUp 0.6s ease-out;
}

.stat-card {
  background: var(--gradient-card);
  backdrop-filter: blur(20px);
  border: 1px solid var(--border-color);
  border-radius: 16px;
  padding: 24px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 4px 16px var(--shadow-light);
}

.stat-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 16px 48px var(--shadow-medium);
}

.stat-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.stat-icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
}

.stat-icon.running {
  background: linear-gradient(135deg, var(--accent-green) 0%, #30d158 100%);
}

.stat-icon.images {
  background: linear-gradient(135deg, var(--accent-blue) 0%, #5856d6 100%);
}

.stat-icon.cpu {
  background: linear-gradient(135deg, var(--accent-red) 0%, #ff6b6b 100%);
}

.stat-icon.memory {
  background: linear-gradient(135deg, var(--accent-orange) 0%, #ffb340 100%);
}

.stat-trend {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  font-weight: 600;
  padding: 4px 8px;
  border-radius: 6px;
}

.stat-trend.up {
  background: rgba(52, 199, 89, 0.1);
  color: var(--accent-green);
}

.stat-trend.down {
  background: rgba(255, 59, 48, 0.1);
  color: var(--accent-red);
}

.stat-trend.stable {
  background: rgba(142, 142, 147, 0.1);
  color: var(--text-secondary);
}

.stat-content {
  text-align: left;
}

.stat-value {
  font-size: 36px;
  font-weight: 700;
  color: var(--text-primary);
  margin: 0 0 8px 0;
  line-height: 1;
}

.stat-label {
  font-size: 14px;
  color: var(--text-secondary);
  margin: 0 0 8px 0;
  font-weight: 500;
}

.stat-detail {
  font-size: 12px;
  color: var(--text-tertiary);
  font-family: 'SF Mono', Monaco, monospace;
}

/* 概览部分 */
.overview-section,
.performance-section,
.system-info-section {
  margin-bottom: 40px;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.section-title {
  font-size: 24px;
  font-weight: 700;
  color: var(--text-primary);
  margin: 0;
}

.section-actions {
  display: flex;
  gap: 12px;
  align-items: center;
}

.view-all-button {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  background: transparent;
  border: 1px solid var(--border-color);
  border-radius: 8px;
  color: var(--accent-blue);
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.view-all-button:hover {
  background: var(--accent-blue);
  color: white;
}

.refresh-indicator {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 6px 12px;
  border-radius: 6px;
  background: var(--bg-tertiary);
  font-size: 12px;
  color: var(--text-secondary);
}

.refresh-indicator.active {
  background: rgba(52, 199, 89, 0.1);
  color: var(--accent-green);
}

.indicator-dot {
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background: currentColor;
}

.refresh-indicator.active .indicator-dot {
  animation: pulse 1.5s infinite;
}

/* 容器概览 */
.container-overview {
  background: var(--glass-bg);
  backdrop-filter: blur(20px);
  border: 1px solid var(--border-color);
  border-radius: 16px;
  padding: 24px;
}

.container-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.container-item {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 16px;
  background: var(--bg-tertiary);
  border-radius: 12px;
  transition: all 0.2s ease;
}

.container-item:hover {
  background: var(--border-color);
  transform: translateX(4px);
}

.container-status {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 4px 8px;
  border-radius: 6px;
  font-size: 11px;
  font-weight: 600;
  min-width: 80px;
}

.container-status.running {
  background: rgba(52, 199, 89, 0.1);
  color: var(--accent-green);
}

.container-status.stopped {
  background: rgba(255, 59, 48, 0.1);
  color: var(--accent-red);
}

.status-dot {
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background: currentColor;
}

.container-info {
  flex: 1;
}

.container-name {
  font-size: 16px;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0 0 4px 0;
}

.container-image {
  font-size: 13px;
  color: var(--text-secondary);
  margin: 0;
  font-family: 'SF Mono', Monaco, monospace;
}

.container-meta {
  display: flex;
  flex-direction: column;
  gap: 4px;
  align-items: flex-end;
  font-size: 12px;
  color: var(--text-tertiary);
  font-family: 'SF Mono', Monaco, monospace;
}

.container-arch {
  background: var(--accent-blue);
  color: white;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 10px;
  font-weight: 600;
}

.container-ip {
  color: var(--text-secondary);
}

/* 性能监控 */
.performance-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 24px;
}

.performance-card {
  background: var(--glass-bg);
  backdrop-filter: blur(20px);
  border: 1px solid var(--border-color);
  border-radius: 16px;
  padding: 24px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.performance-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 12px 32px var(--shadow-light);
}

.performance-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.performance-header h4 {
  font-size: 16px;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0;
}

.performance-value {
  font-size: 24px;
  font-weight: 700;
  color: var(--accent-blue);
  font-family: 'SF Mono', Monaco, monospace;
}

.performance-chart {
  margin-top: 16px;
}

.progress-bar {
  width: 100%;
  height: 8px;
  background: var(--bg-tertiary);
  border-radius: 4px;
  overflow: hidden;
  position: relative;
}

.progress-fill {
  height: 100%;
  border-radius: 4px;
  transition: width 0.8s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
}

.progress-fill.cpu {
  background: linear-gradient(90deg, var(--accent-green) 0%, var(--accent-orange) 70%, var(--accent-red) 100%);
}

.progress-fill.memory {
  background: linear-gradient(90deg, var(--accent-blue) 0%, var(--accent-orange) 70%, var(--accent-red) 100%);
}

.progress-fill::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(90deg, transparent 0%, rgba(255, 255, 255, 0.3) 50%, transparent 100%);
  animation: shimmer 2s infinite;
}

@keyframes shimmer {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

.network-indicator {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0;
}

.network-indicator .upload,
.network-indicator .download {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 14px;
  font-weight: 600;
  font-family: 'SF Mono', Monaco, monospace;
}

.network-indicator .upload {
  color: var(--accent-green);
}

.network-indicator .download {
  color: var(--accent-blue);
}

/* 系统信息网格 */
.system-info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 24px;
}

.info-card {
  background: var(--glass-bg);
  backdrop-filter: blur(20px);
  border: 1px solid var(--border-color);
  border-radius: 16px;
  padding: 24px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.info-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 12px 32px var(--shadow-light);
}

.info-header {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 20px;
}

.info-icon {
  font-size: 24px;
}

.info-header h3 {
  font-size: 18px;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0;
}

.info-content {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 14px;
}

.info-item .label {
  color: var(--text-secondary);
  font-weight: 500;
}

.info-item .value {
  color: var(--text-primary);
  font-weight: 600;
  font-family: 'SF Mono', Monaco, monospace;
}

.info-item .value.status-running {
  color: var(--accent-green);
  display: flex;
  align-items: center;
  gap: 6px;
}

.info-item .value.status-running::before {
  content: '';
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background: currentColor;
  animation: pulse 2s infinite;
}
</style>
