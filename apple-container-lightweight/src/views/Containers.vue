<template>
  <div class="containers-page">
    <!-- 页面标题 -->
    <div class="page-header">
      <div class="header-content">
        <h1 class="page-title">
          <span class="title-icon">📦</span>
          容器管理
        </h1>
        <p class="page-description">管理和监控您的Apple容器</p>
      </div>
      <div class="header-actions">
        <div class="search-box">
          <input 
            type="text" 
            placeholder="搜索容器..." 
            class="search-input"
            v-model="searchText"
          >
        </div>
        <button class="action-button primary" @click="refreshData">
          <span class="button-icon">🔄</span>
          刷新
        </button>
        <button class="action-button success" @click="createContainer">
          <span class="button-icon">➕</span>
          新建容器
        </button>
      </div>
    </div>

    <!-- 容器列表 -->
    <div class="containers-grid" v-if="!loading">
      <div 
        v-for="container in filteredContainers" 
        :key="container.id"
        class="container-card"
      >
        <div class="card-header">
          <div class="container-status" :class="container.state">
            <div class="status-dot"></div>
            <span class="status-text">{{ getStatusText(container.state) }}</span>
          </div>
          <div class="container-actions">
            <button 
              class="action-btn" 
              @click="viewLogs(container)" 
              title="查看日志"
            >
              📄
            </button>
            <button 
              class="action-btn primary" 
              @click="startContainerAction(container)" 
              title="启动容器"
              :disabled="container.state === 'running'"
            >
              ▶️
            </button>
            <button 
              class="action-btn danger" 
              @click="stopContainerAction(container)" 
              title="停止容器"
              :disabled="container.state === 'stopped'"
            >
              ⏹️
            </button>
          </div>
        </div>
        
        <div class="card-content">
          <h3 class="container-name">{{ container.name }}</h3>
          <div class="container-details">
            <div class="detail-item">
              <span class="label">镜像:</span>
              <span class="value">{{ container.image }}</span>
            </div>
            <div class="detail-item">
              <span class="label">架构:</span>
              <span class="value">{{ container.arch }}</span>
            </div>
            <div class="detail-item">
              <span class="label">IP地址:</span>
              <span class="value">{{ container.addr }}</span>
            </div>
            <div class="detail-item">
              <span class="label">系统:</span>
              <span class="value">{{ container.os }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 加载状态 -->
    <div v-if="loading" class="loading-state">
      <div class="spinner">⚡</div>
      <p>加载容器列表...</p>
    </div>

    <!-- 空状态 -->
    <div v-if="!loading && filteredContainers.length === 0" class="empty-state">
      <div class="empty-icon">📦</div>
      <h3>暂无容器</h3>
      <p>您还没有创建任何容器，点击上方按钮开始创建</p>
      <button class="action-button primary" @click="createContainer">
        <span class="button-icon">➕</span>
        创建第一个容器
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { 
  getContainerList,
  startContainer,
  stopContainer,
  getContainerLogs
} from '../api/container'
import type { AppleContainer } from '../api/container'

const loading = ref(false)
const searchText = ref('')
const containerList = ref<AppleContainer[]>([])

// 过滤容器列表
const filteredContainers = computed(() => {
  if (!searchText.value) return containerList.value
  return containerList.value.filter(container => 
    container.name.toLowerCase().includes(searchText.value.toLowerCase()) ||
    container.image.toLowerCase().includes(searchText.value.toLowerCase())
  )
})

const getStatusText = (status: string) => {
  const statusMap: Record<string, string> = {
    running: '运行中',
    stopped: '已停止',
    paused: '已暂停',
    restarting: '重启中'
  }
  return statusMap[status] || status
}

const loadData = async () => {
  loading.value = true
  try {
    const containers = await getContainerList()
    containerList.value = containers
  } catch (error) {
    console.error('加载容器列表失败:', error)
  } finally {
    loading.value = false
  }
}

const refreshData = () => {
  loadData()
}

const createContainer = () => {
  // TODO: 实现创建容器功能
  alert('创建容器功能开发中...')
}

const viewLogs = async (container: AppleContainer) => {
  try {
    const logs = await getContainerLogs(container.id)
    // 这里可以打开一个模态框显示日志
    alert(`容器 ${container.name} 的日志:\n\n${logs}`)
  } catch (error) {
    console.error('获取日志失败:', error)
    alert('获取日志失败')
  }
}

const startContainerAction = async (container: AppleContainer) => {
  try {
    const success = await startContainer(container.id)
    if (success) {
      console.log('容器启动成功:', container.name)
      await loadData() // 刷新数据
    } else {
      alert('启动容器失败')
    }
  } catch (error) {
    console.error('启动容器失败:', error)
    alert('启动容器失败')
  }
}

const stopContainerAction = async (container: AppleContainer) => {
  try {
    const success = await stopContainer(container.id)
    if (success) {
      console.log('容器停止成功:', container.name)
      await loadData() // 刷新数据
    } else {
      alert('停止容器失败')
    }
  } catch (error) {
    console.error('停止容器失败:', error)
    alert('停止容器失败')
  }
}

onMounted(() => {
  loadData()
})
</script>

<style scoped>
.containers-page {
  padding: 24px;
  max-width: 1400px;
  margin: 0 auto;
}

/* 页面标题 */
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 32px;
  padding-bottom: 24px;
  border-bottom: 1px solid var(--border-color);
  flex-wrap: wrap;
  gap: 16px;
}

.header-content {
  flex: 1;
  min-width: 200px;
}

.page-title {
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 32px;
  font-weight: 700;
  color: var(--text-primary);
  margin: 0 0 8px 0;
}

.title-icon {
  font-size: 28px;
}

.page-description {
  font-size: 16px;
  color: var(--text-secondary);
  margin: 0;
}

.header-actions {
  display: flex;
  gap: 12px;
  align-items: center;
  flex-wrap: wrap;
}

.search-box {
  position: relative;
}

.search-input {
  width: 280px;
  padding: 12px 16px;
  background: var(--glass-bg);
  backdrop-filter: blur(20px);
  border: 1px solid var(--border-color);
  border-radius: 12px;
  color: var(--text-primary);
  font-size: 14px;
  transition: all 0.2s ease;
}

.search-input:focus {
  outline: none;
  border-color: var(--accent-blue);
  box-shadow: 0 0 0 3px rgba(0, 122, 255, 0.1);
}

.action-button {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 20px;
  background: var(--glass-bg);
  backdrop-filter: blur(20px);
  border: 1px solid var(--border-color);
  border-radius: 12px;
  color: var(--text-primary);
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  white-space: nowrap;
}

.action-button:hover {
  background: var(--bg-tertiary);
  transform: translateY(-2px);
  box-shadow: 0 8px 24px var(--shadow-light);
}

.action-button.primary {
  background: var(--accent-blue);
  color: white;
  border: none;
}

.action-button.primary:hover {
  background: #0056cc;
}

.action-button.success {
  background: var(--accent-green);
  color: white;
  border: none;
}

.action-button.success:hover {
  background: #28a745;
}

.button-icon {
  font-size: 16px;
}

/* 容器网格 */
.containers-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
  gap: 24px;
}

.container-card {
  background: var(--glass-bg);
  backdrop-filter: blur(20px);
  border: 1px solid var(--border-color);
  border-radius: 16px;
  padding: 20px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.container-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 16px 48px var(--shadow-medium);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.container-status {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 6px 12px;
  border-radius: 8px;
  font-size: 12px;
  font-weight: 600;
}

.container-status.running {
  background: rgba(52, 199, 89, 0.1);
  color: var(--accent-green);
}

.container-status.stopped {
  background: rgba(255, 59, 48, 0.1);
  color: var(--accent-red);
}

.container-status.paused {
  background: rgba(255, 149, 0, 0.1);
  color: var(--accent-orange);
}

.status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: currentColor;
}

.container-actions {
  display: flex;
  gap: 8px;
}

.action-btn {
  width: 32px;
  height: 32px;
  border: none;
  border-radius: 8px;
  background: var(--bg-tertiary);
  color: var(--text-secondary);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  transition: all 0.2s ease;
}

.action-btn:hover:not(:disabled) {
  background: var(--border-color);
  color: var(--text-primary);
  transform: scale(1.05);
}

.action-btn.primary {
  background: var(--accent-blue);
  color: white;
}

.action-btn.danger {
  background: var(--accent-red);
  color: white;
}

.action-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.card-content {
  text-align: left;
}

.container-name {
  font-size: 18px;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0 0 16px 0;
}

.container-details {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.detail-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 14px;
}

.detail-item .label {
  color: var(--text-secondary);
  font-weight: 500;
}

.detail-item .value {
  color: var(--text-primary);
  font-weight: 600;
  font-family: 'SF Mono', Monaco, monospace;
}

/* 加载和空状态 */
.loading-state,
.empty-state {
  text-align: center;
  padding: 80px 20px;
}

.spinner {
  font-size: 48px;
  margin-bottom: 16px;
  display: inline-block;
  animation: pulse 2s infinite;
}

.empty-icon {
  font-size: 64px;
  margin-bottom: 16px;
}

.loading-state p,
.empty-state h3 {
  font-size: 18px;
  color: var(--text-primary);
  margin: 0 0 8px 0;
}

.empty-state p {
  font-size: 14px;
  color: var(--text-secondary);
  margin: 0 0 24px 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .containers-grid {
    grid-template-columns: 1fr;
  }
  
  .page-header {
    flex-direction: column;
    align-items: stretch;
  }
  
  .header-actions {
    justify-content: stretch;
  }
  
  .search-input {
    width: 100%;
  }
  
  .action-button {
    flex: 1;
    justify-content: center;
  }
}
</style>
