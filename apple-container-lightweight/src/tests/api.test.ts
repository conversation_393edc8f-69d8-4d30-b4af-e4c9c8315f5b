import { describe, it, expect, vi, beforeEach } from 'vitest'
import { 
  getContainerList, 
  getImageList, 
  getSystemInfo,
  startContainer,
  stopContainer,
  getContainerLogs
} from '../api/container'

// Mock fetch
global.fetch = vi.fn()

describe('Container API', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  describe('getContainerList', () => {
    it('should return container list when API is available', async () => {
      const mockContainers = [
        {
          id: 'test-container',
          name: 'test-container',
          image: 'nginx:latest',
          os: 'linux',
          arch: 'arm64',
          state: 'running',
          addr: '*************'
        }
      ]

      ;(fetch as any).mockResolvedValueOnce({
        ok: true,
        json: async () => mockContainers
      })

      const result = await getContainerList()
      expect(result).toEqual(mockContainers)
      expect(fetch).toHaveBeenCalledWith('http://localhost:3001/api/containers', {
        headers: {
          'Content-Type': 'application/json'
        }
      })
    })

    it('should fallback to mock data when API fails', async () => {
      ;(fetch as any).mockRejectedValueOnce(new Error('Network error'))

      const result = await getContainerList()
      expect(Array.isArray(result)).toBe(true)
      expect(result.length).toBeGreaterThanOrEqual(0)
    })
  })

  describe('getImageList', () => {
    it('should return image list when API is available', async () => {
      const mockImages = [
        {
          id: 'nginx-latest',
          name: 'nginx',
          tag: 'latest',
          digest: 'sha256:abc123',
          architecture: 'arm64'
        }
      ]

      ;(fetch as any).mockResolvedValueOnce({
        ok: true,
        json: async () => mockImages
      })

      const result = await getImageList()
      expect(result).toEqual(mockImages)
    })
  })

  describe('getSystemInfo', () => {
    it('should return system information', async () => {
      const mockSystemInfo = {
        containerCount: 2,
        runningContainers: 1,
        imageCount: 4,
        version: 'Apple Container 2025.6',
        apiServerStatus: 'running',
        architecture: 'Apple M4',
        platform: 'macOS Tahoe 26.0'
      }

      ;(fetch as any).mockResolvedValueOnce({
        ok: true,
        json: async () => mockSystemInfo
      })

      const result = await getSystemInfo()
      expect(result).toEqual(mockSystemInfo)
    })
  })

  describe('Container Operations', () => {
    it('should start container successfully', async () => {
      ;(fetch as any).mockResolvedValueOnce({
        ok: true,
        json: async () => ({ success: true, message: '容器启动成功' })
      })

      const result = await startContainer('test-container')
      expect(result).toBe(true)
      expect(fetch).toHaveBeenCalledWith(
        'http://localhost:3001/api/containers/test-container/start',
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          }
        }
      )
    })

    it('should stop container successfully', async () => {
      ;(fetch as any).mockResolvedValueOnce({
        ok: true,
        json: async () => ({ success: true, message: '容器停止成功' })
      })

      const result = await stopContainer('test-container')
      expect(result).toBe(true)
    })

    it('should handle container operation failures', async () => {
      ;(fetch as any).mockRejectedValueOnce(new Error('Network error'))

      const result = await startContainer('test-container')
      expect(result).toBe(false)
    })
  })

  describe('getContainerLogs', () => {
    it('should return container logs', async () => {
      const mockLogs = 'Container log output...'
      ;(fetch as any).mockResolvedValueOnce({
        ok: true,
        json: async () => ({ logs: mockLogs })
      })

      const result = await getContainerLogs('test-container', 100)
      expect(result).toBe(mockLogs)
      expect(fetch).toHaveBeenCalledWith(
        'http://localhost:3001/api/containers/test-container/logs?lines=100',
        {
          headers: {
            'Content-Type': 'application/json'
          }
        }
      )
    })

    it('should handle log retrieval failures', async () => {
      ;(fetch as any).mockRejectedValueOnce(new Error('Network error'))

      const result = await getContainerLogs('test-container')
      expect(result).toBe('无法获取日志')
    })
  })

  describe('Error Handling', () => {
    it('should handle HTTP errors gracefully', async () => {
      ;(fetch as any).mockResolvedValueOnce({
        ok: false,
        status: 500,
        json: async () => ({ error: 'Internal Server Error' })
      })

      const result = await getContainerList()
      expect(Array.isArray(result)).toBe(true)
    })

    it('should handle network timeouts', async () => {
      ;(fetch as any).mockImplementationOnce(() => 
        new Promise((_, reject) => 
          setTimeout(() => reject(new Error('Timeout')), 100)
        )
      )

      const result = await getSystemInfo()
      expect(result.version).toBe('Unknown')
    })
  })
})

describe('API Performance', () => {
  it('should complete API calls within reasonable time', async () => {
    ;(fetch as any).mockResolvedValueOnce({
      ok: true,
      json: async () => []
    })

    const startTime = Date.now()
    await getContainerList()
    const endTime = Date.now()

    expect(endTime - startTime).toBeLessThan(5000) // 5秒内完成
  })

  it('should handle concurrent API calls', async () => {
    ;(fetch as any).mockResolvedValue({
      ok: true,
      json: async () => ({ success: true })
    })

    const promises = [
      getContainerList(),
      getImageList(),
      getSystemInfo()
    ]

    const results = await Promise.all(promises)
    expect(results).toHaveLength(3)
  })
})
