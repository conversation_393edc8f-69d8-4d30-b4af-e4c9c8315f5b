import { describe, it, expect, vi, beforeEach } from 'vitest'
import { mount } from '@vue/test-utils'
import { nextTick } from 'vue'
import PerformanceChart from '../components/PerformanceChart.vue'

// Mock ECharts
vi.mock('echarts/core', () => ({
  use: vi.fn()
}))

vi.mock('echarts/renderers', () => ({
  CanvasRenderer: {}
}))

vi.mock('echarts/charts', () => ({
  LineChart: {},
  BarChart: {}
}))

vi.mock('echarts/components', () => ({
  TitleComponent: {},
  TooltipComponent: {},
  LegendComponent: {},
  GridComponent: {},
  DataZoomComponent: {}
}))

vi.mock('vue-echarts', () => ({
  default: {
    name: 'VChart',
    template: '<div class="mock-chart"></div>',
    props: ['option', 'autoresize'],
    emits: ['click']
  },
  THEME_KEY: Symbol('theme')
}))

describe('PerformanceChart Component', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  it('should render with default props', () => {
    const wrapper = mount(PerformanceChart)
    
    expect(wrapper.find('.performance-chart-container').exists()).toBe(true)
    expect(wrapper.find('.chart-title').text()).toBe('性能监控')
    expect(wrapper.find('.mock-chart').exists()).toBe(true)
  })

  it('should render with custom title', () => {
    const wrapper = mount(PerformanceChart, {
      props: {
        title: 'CPU 使用率趋势',
        type: 'cpu'
      }
    })
    
    expect(wrapper.find('.chart-title').text()).toBe('CPU 使用率趋势')
  })

  it('should render time period buttons', () => {
    const wrapper = mount(PerformanceChart)
    
    const periodButtons = wrapper.findAll('.period-button')
    expect(periodButtons).toHaveLength(4)
    
    const buttonTexts = periodButtons.map(button => button.text())
    expect(buttonTexts).toEqual(['1小时', '6小时', '24小时', '7天'])
  })

  it('should handle period selection', async () => {
    const wrapper = mount(PerformanceChart)
    
    const sixHourButton = wrapper.findAll('.period-button')[1]
    await sixHourButton.trigger('click')
    
    expect(sixHourButton.classes()).toContain('active')
  })

  it('should generate different chart options for different types', () => {
    const cpuWrapper = mount(PerformanceChart, {
      props: { type: 'cpu', title: 'CPU' }
    })
    
    const memoryWrapper = mount(PerformanceChart, {
      props: { type: 'memory', title: 'Memory' }
    })
    
    expect(cpuWrapper.vm.chartOption).toBeDefined()
    expect(memoryWrapper.vm.chartOption).toBeDefined()
    expect(cpuWrapper.vm.chartOption).not.toEqual(memoryWrapper.vm.chartOption)
  })

  it('should handle chart click events', async () => {
    const wrapper = mount(PerformanceChart)
    
    const chart = wrapper.findComponent({ name: 'VChart' })
    await chart.vm.$emit('click', { dataIndex: 0 })
    
    // 验证事件处理
    expect(wrapper.emitted()).toBeDefined()
  })

  it('should be responsive to prop changes', async () => {
    const wrapper = mount(PerformanceChart, {
      props: { title: 'Initial Title' }
    })
    
    expect(wrapper.find('.chart-title').text()).toBe('Initial Title')
    
    await wrapper.setProps({ title: 'Updated Title' })
    expect(wrapper.find('.chart-title').text()).toBe('Updated Title')
  })

  it('should cleanup intervals on unmount', () => {
    const clearIntervalSpy = vi.spyOn(global, 'clearInterval')
    const wrapper = mount(PerformanceChart)
    
    wrapper.unmount()
    
    expect(clearIntervalSpy).toHaveBeenCalled()
  })
})

describe('Chart Data Generation', () => {
  it('should generate appropriate data points for different periods', () => {
    const wrapper = mount(PerformanceChart)
    const vm = wrapper.vm as any
    
    const oneHourData = vm.generateMockData('cpu', '1h')
    const oneDayData = vm.generateMockData('cpu', '24h')
    
    expect(oneHourData.data.length).toBeGreaterThan(0)
    expect(oneDayData.data.length).toBeGreaterThan(oneHourData.data.length)
    expect(oneHourData.categories.length).toBe(oneHourData.data.length)
  })

  it('should generate data within expected ranges', () => {
    const wrapper = mount(PerformanceChart, {
      props: { type: 'cpu' }
    })
    const vm = wrapper.vm as any
    
    const data = vm.generateMockData('cpu', '1h')
    
    data.data.forEach((value: number) => {
      expect(value).toBeGreaterThanOrEqual(0)
      expect(value).toBeLessThanOrEqual(100)
    })
  })

  it('should generate different data for different chart types', () => {
    const wrapper = mount(PerformanceChart)
    const vm = wrapper.vm as any
    
    const cpuData = vm.generateMockData('cpu', '1h')
    const memoryData = vm.generateMockData('memory', '1h')
    const networkData = vm.generateMockData('network', '1h')
    const containerData = vm.generateMockData('containers', '1h')
    
    expect(cpuData.data).not.toEqual(memoryData.data)
    expect(networkData.data).not.toEqual(containerData.data)
    
    // 容器数据应该是整数
    containerData.data.forEach((value: number) => {
      expect(Number.isInteger(value)).toBe(true)
    })
  })
})

describe('Chart Performance', () => {
  it('should render chart within reasonable time', async () => {
    const startTime = Date.now()
    
    const wrapper = mount(PerformanceChart, {
      props: {
        title: 'Performance Test',
        type: 'cpu'
      }
    })
    
    await nextTick()
    
    const endTime = Date.now()
    expect(endTime - startTime).toBeLessThan(1000) // 1秒内渲染完成
    
    wrapper.unmount()
  })

  it('should handle rapid prop updates', async () => {
    const wrapper = mount(PerformanceChart)
    
    // 快速更新props
    for (let i = 0; i < 10; i++) {
      await wrapper.setProps({ title: `Title ${i}` })
    }
    
    expect(wrapper.find('.chart-title').text()).toBe('Title 9')
  })

  it('should not cause memory leaks with intervals', () => {
    const setIntervalSpy = vi.spyOn(global, 'setInterval')
    const clearIntervalSpy = vi.spyOn(global, 'clearInterval')
    
    const wrapper = mount(PerformanceChart)
    
    // 验证设置了定时器
    expect(setIntervalSpy).toHaveBeenCalled()
    
    wrapper.unmount()
    
    // 验证清理了定时器
    expect(clearIntervalSpy).toHaveBeenCalled()
  })
})

describe('Accessibility', () => {
  it('should have proper ARIA attributes', () => {
    const wrapper = mount(PerformanceChart, {
      props: {
        title: 'CPU Usage Chart'
      }
    })
    
    const container = wrapper.find('.performance-chart-container')
    expect(container.exists()).toBe(true)
    
    const title = wrapper.find('.chart-title')
    expect(title.text()).toBe('CPU Usage Chart')
  })

  it('should be keyboard accessible', async () => {
    const wrapper = mount(PerformanceChart)
    
    const buttons = wrapper.findAll('.period-button')
    
    for (const button of buttons) {
      expect(button.element.tagName).toBe('BUTTON')
      // 按钮应该可以通过键盘访问
      await button.trigger('keydown.enter')
      await button.trigger('keydown.space')
    }
  })
})
