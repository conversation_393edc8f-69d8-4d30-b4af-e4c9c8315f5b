// 前端API接口 - 通过HTTP请求与后端通信
const API_BASE_URL = 'http://localhost:3001/api'

// 苹果容器API接口类型定义
export interface AppleContainer {
  id: string
  name: string
  image: string
  os: string
  arch: string
  state: 'running' | 'stopped' | 'paused' | 'restarting'
  addr: string
  ports?: string
  created?: string
  uptime?: string
}

export interface AppleImage {
  id: string
  name: string
  tag: string
  digest: string
  size?: string
  created?: string
  architecture?: string
}

export interface AppleSystemInfo {
  containerCount: number
  runningContainers: number
  imageCount: number
  version: string
  apiServerStatus: 'running' | 'stopped'
  architecture: string
  platform: string
}

// 模拟API调用 - 在实际项目中应该调用后端API
async function executeContainerCommand(command: string): Promise<string> {
  // 模拟网络延迟
  await new Promise(resolve => setTimeout(resolve, 300))

  // 根据命令返回模拟数据
  switch (command) {
    case 'list':
      return `ID               IMAGE                           OS     ARCH   STATE    ADDR
apple-n8n-fixed  docker.io/n8nio/n8n:latest      linux  arm64  running  192.168.64.5
apple-redis      docker.io/library/redis:alpine  linux  arm64  running  192.168.64.2`

    case 'images list':
      return `NAME                      TAG        DIGEST
node                      18-alpine  8d6421d663b4c28fd3ebc498...
redis                     alpine     25c0ae32c6c2301798579f59...
n8nio/n8n                 latest     ee9bcd832eee4221d4944003...
ghcr.io/tbxark/mcp-proxy  latest     b642cbf9bac4a9264ec791e6...`

    case 'system status':
      return `Verifying apiserver is running...
apiserver is running`

    default:
      if (command.startsWith('logs ')) {
        return `[2024-01-01 12:00:00] Container started successfully
[2024-01-01 12:00:01] Application initialized
[2024-01-01 12:00:02] Server listening on port 3000
[2024-01-01 12:00:03] Ready to accept connections`
      }
      return 'Command executed successfully'
  }
}

// 解析容器列表输出
function parseContainerList(output: string): AppleContainer[] {
  const lines = output.split('\n').filter(line => line.trim())
  const containers: AppleContainer[] = []

  // 跳过标题行
  for (let i = 1; i < lines.length; i++) {
    const line = lines[i].trim()
    if (!line) continue

    const parts = line.split(/\s+/)
    if (parts.length >= 6) {
      containers.push({
        id: parts[0],
        name: parts[0], // 苹果容器中ID就是名称
        image: parts[1],
        os: parts[2],
        arch: parts[3],
        state: parts[4] as any,
        addr: parts[5]
      })
    }
  }

  return containers
}

// 解析镜像列表输出
function parseImageList(output: string): AppleImage[] {
  const lines = output.split('\n').filter(line => line.trim())
  const images: AppleImage[] = []

  // 跳过标题行
  for (let i = 1; i < lines.length; i++) {
    const line = lines[i].trim()
    if (!line) continue

    const parts = line.split(/\s+/)
    if (parts.length >= 3) {
      const [name, tag] = parts[0].includes(':') ? parts[0].split(':') : [parts[0], parts[1]]
      images.push({
        id: `${parts[0]}-${parts[1]}`,
        name: name,
        tag: tag || parts[1],
        digest: parts[2],
        architecture: 'arm64' // 苹果容器主要是ARM64
      })
    }
  }

  return images
}

// HTTP请求辅助函数
async function apiRequest(endpoint: string, options: RequestInit = {}): Promise<any> {
  try {
    const response = await fetch(`${API_BASE_URL}${endpoint}`, {
      headers: {
        'Content-Type': 'application/json',
        ...options.headers,
      },
      ...options,
    })

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }

    return await response.json()
  } catch (error) {
    console.error(`API请求失败: ${endpoint}`, error)
    // 如果后端不可用，回退到模拟数据
    return await fallbackToMockData(endpoint)
  }
}

// 回退到模拟数据
async function fallbackToMockData(endpoint: string): Promise<any> {
  console.log('回退到模拟数据:', endpoint)
  await new Promise(resolve => setTimeout(resolve, 300))

  switch (endpoint) {
    case '/containers':
      return parseContainerList(await executeContainerCommand('list'))
    case '/images':
      return parseImageList(await executeContainerCommand('images list'))
    case '/system':
      const [containers, images, statusOutput] = await Promise.all([
        parseContainerList(await executeContainerCommand('list')),
        parseImageList(await executeContainerCommand('images list')),
        executeContainerCommand('system status')
      ])

      const runningContainers = containers.filter((c: any) => c.state === 'running').length
      const apiServerStatus = statusOutput.includes('apiserver is running') ? 'running' : 'stopped'

      return {
        containerCount: containers.length,
        runningContainers,
        imageCount: images.length,
        version: 'Apple Container 2025.6',
        apiServerStatus,
        architecture: 'Apple M4',
        platform: 'macOS Tahoe 26.0'
      }
    default:
      return {}
  }
}

// 获取容器列表
export const getContainerList = async (): Promise<AppleContainer[]> => {
  try {
    return await apiRequest('/containers')
  } catch (error) {
    console.error('获取容器列表失败:', error)
    return []
  }
}

// 获取镜像列表
export const getImageList = async (): Promise<AppleImage[]> => {
  try {
    return await apiRequest('/images')
  } catch (error) {
    console.error('获取镜像列表失败:', error)
    return []
  }
}

// 获取系统信息
export const getSystemInfo = async (): Promise<AppleSystemInfo> => {
  try {
    return await apiRequest('/system')
  } catch (error) {
    console.error('获取系统信息失败:', error)
    return {
      containerCount: 0,
      runningContainers: 0,
      imageCount: 0,
      version: 'Unknown',
      apiServerStatus: 'stopped',
      architecture: 'Unknown',
      platform: 'Unknown'
    }
  }
}

// 容器操作函数
export const startContainer = async (containerId: string): Promise<boolean> => {
  try {
    const result = await apiRequest(`/containers/${containerId}/start`, {
      method: 'POST'
    })
    return result.success
  } catch (error) {
    console.error(`启动容器失败: ${containerId}`, error)
    return false
  }
}

export const stopContainer = async (containerId: string): Promise<boolean> => {
  try {
    const result = await apiRequest(`/containers/${containerId}/stop`, {
      method: 'POST'
    })
    return result.success
  } catch (error) {
    console.error(`停止容器失败: ${containerId}`, error)
    return false
  }
}

export const restartContainer = async (containerId: string): Promise<boolean> => {
  try {
    const result = await apiRequest(`/containers/${containerId}/restart`, {
      method: 'POST'
    })
    return result.success
  } catch (error) {
    console.error(`重启容器失败: ${containerId}`, error)
    return false
  }
}

export const removeContainer = async (containerId: string): Promise<boolean> => {
  try {
    const result = await apiRequest(`/containers/${containerId}`, {
      method: 'DELETE'
    })
    return result.success
  } catch (error) {
    console.error(`删除容器失败: ${containerId}`, error)
    return false
  }
}

export const getContainerLogs = async (containerId: string, lines: number = 100): Promise<string> => {
  try {
    const result = await apiRequest(`/containers/${containerId}/logs?lines=${lines}`)
    return result.logs || '无日志数据'
  } catch (error) {
    console.error(`获取容器日志失败: ${containerId}`, error)
    return '无法获取日志'
  }
}

// 镜像操作函数
export const pullImage = async (imageName: string): Promise<boolean> => {
  try {
    const result = await apiRequest('/images/pull', {
      method: 'POST',
      body: JSON.stringify({ imageName })
    })
    return result.success
  } catch (error) {
    console.error(`拉取镜像失败: ${imageName}`, error)
    return false
  }
}

export const removeImage = async (imageId: string): Promise<boolean> => {
  try {
    const result = await apiRequest(`/images/${imageId}`, {
      method: 'DELETE'
    })
    return result.success
  } catch (error) {
    console.error(`删除镜像失败: ${imageId}`, error)
    return false
  }
}

// 为了兼容现有组件，提供适配器函数
export const getContainerListCompat = async () => {
  const containers = await getContainerList()
  return containers.map(container => ({
    id: container.id,
    name: container.name,
    status: container.state,
    ports: container.addr,
    image: container.image
  }))
}

export const getImageListCompat = async () => {
  const images = await getImageList()
  return images.map(image => ({
    id: image.id,
    name: image.name,
    tag: image.tag,
    size: image.size || 'Unknown'
  }))
}

export const getSystemInfoCompat = async () => {
  const systemInfo = await getSystemInfo()
  return {
    containerCount: systemInfo.runningContainers,
    imageCount: systemInfo.imageCount,
    version: systemInfo.version
  }
}
