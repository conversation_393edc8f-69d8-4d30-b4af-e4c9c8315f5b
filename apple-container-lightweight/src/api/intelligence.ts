// Apple Intelligence Center API 服务

// 基础配置
const API_BASE_URL = 'http://localhost:3002/api'
const HUGGING_FACE_API = 'https://huggingface.co/api'
const GITHUB_API = 'https://api.github.com'

// 通用请求函数
async function apiRequest(url: string, options: RequestInit = {}): Promise<any> {
  try {
    const response = await fetch(url, {
      headers: {
        'Content-Type': 'application/json',
        ...options.headers
      },
      ...options
    })
    
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }
    
    return await response.json()
  } catch (error) {
    console.error('API request failed:', error)
    throw error
  }
}

// 镜像仓库相关接口
export interface ImageRepository {
  id: string
  name: string
  description: string
  tags: string[]
  downloads: number
  rating: number
  architecture: string
  version: string
  lastUpdated: string
  category: string
  source: 'huggingface' | 'github' | 'dockerhub' | 'custom'
  url: string
  logo?: string
}

export interface ImageCategory {
  id: string
  name: string
  description: string
  icon: string
  count: number
}

// 获取镜像仓库列表
export async function getImageRepositories(params?: {
  category?: string
  search?: string
  page?: number
  limit?: number
}): Promise<ImageRepository[]> {
  try {
    const queryParams = new URLSearchParams()
    if (params?.category) queryParams.append('category', params.category)
    if (params?.search) queryParams.append('search', params.search)
    if (params?.page) queryParams.append('page', params.page.toString())
    if (params?.limit) queryParams.append('limit', params.limit.toString())
    
    const url = `${API_BASE_URL}/intelligence/images?${queryParams}`
    return await apiRequest(url)
  } catch (error) {
    // 返回模拟数据作为后备
    return getMockImageRepositories()
  }
}

// 获取镜像分类
export async function getImageCategories(): Promise<ImageCategory[]> {
  try {
    return await apiRequest(`${API_BASE_URL}/intelligence/categories`)
  } catch (error) {
    return [
      { id: 'ai-ml', name: 'AI/ML', description: '人工智能和机器学习', icon: 'brain', count: 234 },
      { id: 'databases', name: '数据库', description: '各种数据库系统', icon: 'database', count: 156 },
      { id: 'web-servers', name: 'Web服务', description: 'Web服务器和应用', icon: 'server', count: 189 },
      { id: 'dev-tools', name: '开发工具', description: '开发和构建工具', icon: 'toolbox', count: 298 },
      { id: 'monitoring', name: '监控', description: '监控和观测工具', icon: 'monitoring', count: 87 }
    ]
  }
}

// Hugging Face API 集成
export async function searchHuggingFaceModels(query: string, limit: number = 20): Promise<any[]> {
  try {
    const url = `${HUGGING_FACE_API}/models?search=${encodeURIComponent(query)}&limit=${limit}`
    const response = await fetch(url)
    return await response.json()
  } catch (error) {
    console.error('Hugging Face API error:', error)
    return []
  }
}

// GitHub Container Registry API 集成
export async function searchGitHubPackages(query: string, packageType: string = 'container'): Promise<any[]> {
  try {
    const url = `${GITHUB_API}/search/repositories?q=${encodeURIComponent(query)}+in:name`
    const response = await fetch(url, {
      headers: {
        'Accept': 'application/vnd.github.v3+json'
      }
    })
    const data = await response.json()
    return data.items || []
  } catch (error) {
    console.error('GitHub API error:', error)
    return []
  }
}

// 资讯相关接口
export interface NewsArticle {
  id: string
  title: string
  summary: string
  content: string
  image: string
  category: string
  tags: string[]
  author: string
  publishedAt: string
  readingTime: number
  likes: number
  views: number
  url: string
}

// 获取新闻文章
export async function getNewsArticles(params?: {
  category?: string
  limit?: number
}): Promise<NewsArticle[]> {
  try {
    const queryParams = new URLSearchParams()
    if (params?.category) queryParams.append('category', params.category)
    if (params?.limit) queryParams.append('limit', params.limit.toString())
    
    const url = `${API_BASE_URL}/intelligence/news?${queryParams}`
    return await apiRequest(url)
  } catch (error) {
    return getMockNewsArticles()
  }
}

// 教育资源相关接口
export interface Tutorial {
  id: string
  title: string
  description: string
  thumbnail: string
  type: 'video' | 'document' | 'interactive'
  category: string
  difficulty: 'beginner' | 'intermediate' | 'advanced'
  duration: string
  tags: string[]
  author: {
    name: string
    avatar: string
  }
  views: number
  likes: number
  url: string
}

export interface LearningPath {
  id: string
  title: string
  description: string
  icon: string
  level: string
  duration: string
  students: number
  rating: number
  progress: number
  modules: string[]
}

// 获取教程列表
export async function getTutorials(params?: {
  difficulty?: string
  category?: string
  type?: string
}): Promise<Tutorial[]> {
  try {
    const queryParams = new URLSearchParams()
    if (params?.difficulty) queryParams.append('difficulty', params.difficulty)
    if (params?.category) queryParams.append('category', params.category)
    if (params?.type) queryParams.append('type', params.type)
    
    const url = `${API_BASE_URL}/intelligence/tutorials?${queryParams}`
    return await apiRequest(url)
  } catch (error) {
    return getMockTutorials()
  }
}

// 获取学习路径
export async function getLearningPaths(): Promise<LearningPath[]> {
  try {
    return await apiRequest(`${API_BASE_URL}/intelligence/learning-paths`)
  } catch (error) {
    return getMockLearningPaths()
  }
}

// 社区相关接口
export interface Discussion {
  id: string
  title: string
  content: string
  tags: string[]
  author: {
    name: string
    avatar: string
    badge?: string
  }
  createdAt: string
  replies: number
  likes: number
  views: number
}

// 获取社区讨论
export async function getDiscussions(params?: {
  category?: string
  sort?: 'hot' | 'new' | 'top'
}): Promise<Discussion[]> {
  try {
    const queryParams = new URLSearchParams()
    if (params?.category) queryParams.append('category', params.category)
    if (params?.sort) queryParams.append('sort', params.sort)
    
    const url = `${API_BASE_URL}/intelligence/discussions?${queryParams}`
    return await apiRequest(url)
  } catch (error) {
    return getMockDiscussions()
  }
}

// 工具箱相关接口
export interface Tool {
  id: string
  name: string
  description: string
  icon: string
  category: string
  status: 'online' | 'offline' | 'maintenance'
  rating: number
  users: number
  lastUpdated: string
  features: string[]
  tags: string[]
  url: string
  isBookmarked: boolean
}

// 获取工具列表
export async function getTools(params?: {
  category?: string
  sort?: string
}): Promise<Tool[]> {
  try {
    const queryParams = new URLSearchParams()
    if (params?.category) queryParams.append('category', params.category)
    if (params?.sort) queryParams.append('sort', params.sort)
    
    const url = `${API_BASE_URL}/intelligence/tools?${queryParams}`
    return await apiRequest(url)
  } catch (error) {
    return getMockTools()
  }
}

// 搜索功能
export async function searchIntelligenceContent(query: string, type?: string): Promise<any[]> {
  try {
    const queryParams = new URLSearchParams()
    queryParams.append('q', query)
    if (type) queryParams.append('type', type)
    
    const url = `${API_BASE_URL}/intelligence/search?${queryParams}`
    return await apiRequest(url)
  } catch (error) {
    return []
  }
}

// 模拟数据函数
function getMockImageRepositories(): ImageRepository[] {
  return [
    {
      id: '1',
      name: 'TensorFlow',
      description: '开源机器学习框架',
      tags: ['AI', 'ML', 'Python'],
      downloads: 2400000,
      rating: 4.8,
      architecture: 'ARM64',
      version: '2.15.0',
      lastUpdated: '2天前',
      category: 'ai-ml',
      source: 'dockerhub',
      url: 'https://hub.docker.com/r/tensorflow/tensorflow'
    }
  ]
}

function getMockNewsArticles(): NewsArticle[] {
  return [
    {
      id: '1',
      title: 'Apple Silicon 容器技术突破',
      summary: 'Apple 发布了针对 Apple Silicon 的容器优化技术',
      content: '',
      image: 'https://images.unsplash.com/photo-1611224923853-80b023f02d71?w=400&h=200&fit=crop',
      category: 'Apple',
      tags: ['Apple Silicon', 'Container'],
      author: 'Apple Developer',
      publishedAt: '2小时前',
      readingTime: 5,
      likes: 234,
      views: 1567,
      url: 'https://developer.apple.com/news'
    }
  ]
}

function getMockTutorials(): Tutorial[] {
  return [
    {
      id: '1',
      title: 'Docker 入门教程',
      description: '从零开始学习 Docker 容器技术',
      thumbnail: 'https://images.unsplash.com/photo-1618477247222-acbdb0e159b3?w=400&h=225&fit=crop',
      type: 'video',
      category: '容器技术',
      difficulty: 'beginner',
      duration: '2小时',
      tags: ['Docker', '入门'],
      author: {
        name: 'Docker Expert',
        avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=40&h=40&fit=crop&crop=face'
      },
      views: 12500,
      likes: 892,
      url: 'https://docker-tutorial.com'
    }
  ]
}

function getMockLearningPaths(): LearningPath[] {
  return [
    {
      id: '1',
      title: 'Apple Silicon 容器开发',
      description: '学习在 Apple Silicon 上进行容器开发',
      icon: 'apple',
      level: '初级',
      duration: '6周',
      students: 1247,
      rating: 4.8,
      progress: 35,
      modules: ['Docker 基础', 'Apple Silicon 优化', '实战项目']
    }
  ]
}

function getMockDiscussions(): Discussion[] {
  return [
    {
      id: '1',
      title: 'Apple Silicon Docker 性能优化讨论',
      content: '分享一些 Apple Silicon 上的 Docker 优化经验',
      tags: ['Docker', 'Apple Silicon'],
      author: {
        name: 'TechGuru',
        avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=40&h=40&fit=crop&crop=face',
        badge: 'star'
      },
      createdAt: '2小时前',
      replies: 23,
      likes: 156,
      views: 2340
    }
  ]
}

function getMockTools(): Tool[] {
  return [
    {
      id: '1',
      name: 'Docker Desktop',
      description: '官方 Docker 桌面应用',
      icon: 'containers',
      category: 'development',
      status: 'online',
      rating: 4.8,
      users: 50000,
      lastUpdated: '3天前',
      features: ['容器管理', 'Kubernetes', '镜像构建'],
      tags: ['Docker', 'Container'],
      url: 'https://docker.com/desktop',
      isBookmarked: false
    }
  ]
}

// 缓存管理
class CacheManager {
  private cache = new Map<string, { data: any; timestamp: number; ttl: number }>()
  
  set(key: string, data: any, ttl: number = 300000): void { // 默认5分钟
    this.cache.set(key, {
      data,
      timestamp: Date.now(),
      ttl
    })
  }
  
  get(key: string): any | null {
    const item = this.cache.get(key)
    if (!item) return null
    
    if (Date.now() - item.timestamp > item.ttl) {
      this.cache.delete(key)
      return null
    }
    
    return item.data
  }
  
  clear(): void {
    this.cache.clear()
  }
}

export const cacheManager = new CacheManager()
