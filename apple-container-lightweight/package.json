{"name": "apple-container-studio", "version": "2.0.0", "description": "Professional macOS Container Management Studio", "type": "module", "scripts": {"dev": "vite", "build": "vue-tsc && vite build", "preview": "vite preview", "type-check": "vue-tsc --noEmit", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix --ignore-path .gitignore", "format": "prettier --write src/"}, "dependencies": {"vue": "^3.4.0", "vue-router": "^4.2.0", "pinia": "^2.1.0", "axios": "^1.6.0", "echarts": "^5.4.0", "vue-echarts": "^6.6.0", "socket.io-client": "^4.7.0", "dayjs": "^1.11.0", "lodash-es": "^4.17.0", "@headlessui/vue": "^1.7.0", "@heroicons/vue": "^2.0.0", "tailwindcss": "^3.4.0"}, "devDependencies": {"@vitejs/plugin-vue": "^5.0.0", "vite": "^5.0.0", "typescript": "^5.3.0", "vue-tsc": "^1.8.0", "@types/node": "^20.10.0", "@types/lodash-es": "^4.17.0", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "eslint": "^8.45.0", "eslint-plugin-vue": "^9.17.0", "prettier": "^3.0.0", "autoprefixer": "^10.4.0", "postcss": "^8.4.0"}}