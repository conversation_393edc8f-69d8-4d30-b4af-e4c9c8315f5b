# 🍎 Apple Container Studio

一个采用苹果设计语言打造的现代化Docker容器管理界面。

## ✨ 特性

- **🍎 苹果设计语言**: 采用苹果官方设计规范
- **🌈 毛玻璃效果**: 使用backdrop-filter实现优雅的毛玻璃效果
- **📱 响应式设计**: 支持桌面端、平板和移动端
- **🌙 深色模式**: 自动适配系统主题偏好
- **⚡ 流畅动画**: 使用cubic-bezier缓动函数的丝滑动画
- **🎨 渐变配色**: 苹果经典的蓝紫色渐变配色方案

## 🎨 设计亮点

### 配色方案
- **主色调**: #007AFF (苹果蓝)
- **辅助色**: #5856D6 (苹果紫)
- **成功色**: #34C759 (苹果绿)
- **警告色**: #FF9500 (苹果橙)
- **错误色**: #FF3B30 (苹果红)
- **文字色**: #1D1D1F (苹果黑)
- **次要文字**: #8E8E93 (苹果灰)

### 字体
- **主字体**: SF Pro Display / SF Pro Text
- **等宽字体**: SF Mono
- **回退字体**: -apple-system, BlinkMacSystemFont

### 设计元素
- **圆角**: 12px - 20px不等的圆角设计
- **阴影**: 多层阴影营造深度感
- **毛玻璃**: backdrop-filter: blur(20px)
- **动画**: cubic-bezier(0.4, 0, 0.2, 1) 缓动

## 🚀 快速开始

### 安装依赖
```bash
npm install
```

### 启动开发服务器
```bash
npm run dev
```

### 构建生产版本
```bash
npm run build
```

## 📱 界面预览

### 主要功能区域
1. **顶部导航栏** - 毛玻璃效果的固定导航
2. **统计卡片** - 显示容器、镜像、系统信息
3. **容器列表** - 卡片式容器管理界面
4. **镜像仓库** - 网格式镜像展示
5. **关于区域** - 应用信息和功能标签

### 交互特性
- **悬停效果** - 卡片悬停时的提升动画
- **搜索过滤** - 实时搜索容器
- **状态标识** - 彩色标签显示容器状态
- **操作按钮** - 启动、停止、查看日志等操作

## 🛠 技术栈

- **前端框架**: Vue 3 + TypeScript
- **构建工具**: Vite
- **样式**: 原生CSS (无UI框架依赖)
- **图标**: 使用Emoji替代图标库

## 📝 开发说明

当前版本使用模拟数据展示界面效果。实际部署时需要：

1. 连接真实的Docker API
2. 实现容器操作功能
3. 添加用户认证
4. 配置生产环境

## 🎯 设计理念

这个项目展示了如何在Web应用中实现苹果的设计语言：

- **简洁至上**: 去除不必要的装饰元素
- **注重细节**: 精确的间距、圆角、阴影
- **一致性**: 统一的设计语言和交互模式
- **可访问性**: 支持深色模式和响应式设计

## 📄 许可证

MIT License

---

**Container Studio** - 为开发者打造的优雅容器管理工具 🚀 