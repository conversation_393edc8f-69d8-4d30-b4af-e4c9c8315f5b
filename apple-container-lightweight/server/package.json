{"name": "apple-container-api", "version": "1.0.0", "description": "Apple Container Studio API Server", "main": "index.js", "scripts": {"start": "node index.js", "dev": "nodemon index.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["apple", "container", "docker", "api", "macos"], "author": "Apple Container Studio", "license": "MIT", "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "ws": "^8.14.2"}, "devDependencies": {"nodemon": "^3.0.2"}, "engines": {"node": ">=16.0.0"}}