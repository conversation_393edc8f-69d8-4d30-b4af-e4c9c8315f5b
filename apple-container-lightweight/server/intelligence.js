const express = require('express')
const cors = require('cors')
const app = express()
const port = 3002

// 中间件
app.use(cors())
app.use(express.json())

// 模拟数据
const mockImageRepositories = [
  {
    id: '1',
    name: 'TensorFlow',
    description: '开源机器学习框架，支持深度学习和神经网络训练',
    tags: ['AI', 'ML', 'Python', 'GPU'],
    downloads: 2400000,
    rating: 4.8,
    users: 125000,
    architecture: 'ARM64',
    version: '2.15.0',
    lastUpdated: '2天前',
    category: 'ai-ml',
    source: 'dockerhub',
    url: 'https://hub.docker.com/r/tensorflow/tensorflow',
    logo: 'https://www.tensorflow.org/images/tf_logo_social.png',
    status: 'running',
    featured: true,
    metrics: {
      cpu: 45,
      memory: 68,
      storage: 2048,
      network: 12
    }
  },
  {
    id: '2',
    name: 'PostgreSQL',
    description: '强大的开源关系型数据库系统',
    tags: ['Database', 'SQL', 'ACID'],
    downloads: 1800000,
    rating: 4.9,
    users: 89000,
    architecture: 'Universal',
    version: '16.1',
    lastUpdated: '1周前',
    category: 'databases',
    source: 'dockerhub',
    url: 'https://hub.docker.com/_/postgres',
    logo: 'https://wiki.postgresql.org/images/a/a4/PostgreSQL_logo.3colors.svg',
    status: 'available',
    metrics: {
      cpu: 23,
      memory: 34,
      storage: 1024,
      network: 8
    }
  },
  {
    id: '3',
    name: 'Nginx',
    description: '高性能的HTTP和反向代理服务器',
    tags: ['Web Server', 'Proxy', 'Load Balancer'],
    downloads: 3200000,
    rating: 4.7,
    users: 156000,
    architecture: 'ARM64',
    version: '1.25.3',
    lastUpdated: '3天前',
    category: 'web-services',
    source: 'dockerhub',
    url: 'https://hub.docker.com/_/nginx',
    logo: 'https://nginx.org/nginx.png',
    status: 'running',
    featured: true,
    metrics: {
      cpu: 12,
      memory: 28,
      storage: 512,
      network: 45
    }
  },
  {
    id: '4',
    name: 'Redis',
    description: '高性能内存数据结构存储，用作数据库、缓存和消息代理',
    tags: ['Cache', 'NoSQL', 'In-Memory'],
    downloads: 1500000,
    rating: 4.6,
    users: 78000,
    architecture: 'ARM64',
    version: '7.2.4',
    lastUpdated: '5天前',
    category: 'databases',
    source: 'dockerhub',
    url: 'https://hub.docker.com/_/redis',
    logo: 'https://redis.io/images/redis-white.png',
    status: 'stopped',
    metrics: {
      cpu: 0,
      memory: 0,
      storage: 256,
      network: 0
    }
  },
  {
    id: '5',
    name: 'Node.js',
    description: 'JavaScript运行时环境，基于Chrome V8引擎',
    tags: ['JavaScript', 'Runtime', 'Backend'],
    downloads: 2100000,
    rating: 4.5,
    users: 134000,
    architecture: 'Universal',
    version: '20.11.0',
    lastUpdated: '1天前',
    category: 'dev-tools',
    source: 'dockerhub',
    url: 'https://hub.docker.com/_/node',
    logo: 'https://nodejs.org/static/images/logo.svg',
    status: 'updating',
    featured: true,
    metrics: {
      cpu: 67,
      memory: 45,
      storage: 1536,
      network: 23
    }
  }
]

const mockNewsArticles = [
  {
    id: '1',
    title: 'Apple 发布 macOS Tahoe 26.0：原生容器技术革命性突破',
    summary: '苹果在最新的 macOS Tahoe 26.0 中引入了革命性的原生容器技术，为开发者提供了前所未有的性能和安全性。',
    content: '详细内容...',
    image: 'https://images.unsplash.com/photo-1611224923853-80b023f02d71?w=800&h=400&fit=crop',
    category: 'Apple',
    tags: ['macOS', 'Container', 'Apple Silicon'],
    author: 'Apple Developer',
    publishedAt: '2小时前',
    readingTime: 8,
    likes: 1247,
    views: 5678,
    url: 'https://developer.apple.com/news'
  },
  {
    id: '2',
    title: 'Docker Desktop 支持 Apple Silicon 原生优化',
    summary: 'Docker 宣布对 Apple Silicon 芯片进行深度优化，性能提升高达 300%',
    content: '详细内容...',
    image: 'https://images.unsplash.com/photo-1618477247222-acbdb0e159b3?w=400&h=200&fit=crop',
    category: '容器技术',
    tags: ['Docker', 'Apple Silicon', 'Performance'],
    author: 'Docker Team',
    publishedAt: '4小时前',
    readingTime: 5,
    likes: 892,
    views: 3456,
    url: 'https://docker.com/blog'
  }
]

const mockTutorials = [
  {
    id: '1',
    title: 'Apple Silicon 上的 Docker 性能优化',
    description: '学习如何在 Apple Silicon 芯片上优化 Docker 容器性能',
    thumbnail: 'https://images.unsplash.com/photo-1618477247222-acbdb0e159b3?w=400&h=225&fit=crop',
    type: 'video',
    category: '容器技术',
    difficulty: 'intermediate',
    duration: '45分钟',
    tags: ['Docker', 'Apple Silicon', '性能优化'],
    author: {
      name: 'John Doe',
      avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=40&h=40&fit=crop&crop=face'
    },
    views: 12500,
    likes: 892,
    url: 'https://tutorial.example.com/docker-optimization'
  }
]

const mockTools = [
  {
    id: '1',
    name: 'Docker Desktop',
    description: '官方 Docker 桌面应用，支持 Apple Silicon 优化',
    icon: 'containers',
    category: 'development',
    status: 'online',
    rating: 4.8,
    users: 50000,
    lastUpdated: '3天前',
    features: ['容器管理', 'Kubernetes', '镜像构建'],
    tags: ['Docker', 'Container', 'Development'],
    url: 'https://docker.com/desktop',
    isBookmarked: false
  }
]

// API 路由

// 镜像仓库
app.get('/api/intelligence/images', (req, res) => {
  const { category, search, page = 1, limit = 20 } = req.query
  let filtered = [...mockImageRepositories]
  
  if (category && category !== 'all') {
    filtered = filtered.filter(repo => repo.category === category)
  }
  
  if (search) {
    const searchLower = search.toLowerCase()
    filtered = filtered.filter(repo => 
      repo.name.toLowerCase().includes(searchLower) ||
      repo.description.toLowerCase().includes(searchLower) ||
      repo.tags.some(tag => tag.toLowerCase().includes(searchLower))
    )
  }
  
  const startIndex = (page - 1) * limit
  const endIndex = startIndex + parseInt(limit)
  const paginatedResults = filtered.slice(startIndex, endIndex)
  
  res.json({
    data: paginatedResults,
    total: filtered.length,
    page: parseInt(page),
    limit: parseInt(limit)
  })
})

// 镜像分类
app.get('/api/intelligence/categories', (req, res) => {
  res.json([
    { id: 'featured', name: 'Featured Images', description: '精选镜像', icon: 'star', count: 12, featured: true },
    { id: 'my-images', name: 'My Images', description: '我的镜像', icon: 'person', count: 8 },
    { id: 'ai-ml', name: 'AI & Machine Learning', description: '人工智能和机器学习', icon: 'brain', count: 234 },
    { id: 'databases', name: 'Databases & Storage', description: '数据库和存储系统', icon: 'database', count: 156 },
    { id: 'web-services', name: 'Web Services & APIs', description: 'Web服务和API', icon: 'server', count: 189 },
    { id: 'dev-tools', name: 'Development Tools', description: '开发和构建工具', icon: 'toolbox', count: 298 },
    { id: 'security', name: 'Security & Monitoring', description: '安全和监控工具', icon: 'shield', count: 87 }
  ])
})

// 新闻文章
app.get('/api/intelligence/news', (req, res) => {
  const { category, limit = 10 } = req.query
  let filtered = [...mockNewsArticles]
  
  if (category && category !== 'all') {
    filtered = filtered.filter(article => article.category === category)
  }
  
  res.json(filtered.slice(0, parseInt(limit)))
})

// 教程
app.get('/api/intelligence/tutorials', (req, res) => {
  const { difficulty, category, type } = req.query
  let filtered = [...mockTutorials]
  
  if (difficulty && difficulty !== 'all') {
    filtered = filtered.filter(tutorial => tutorial.difficulty === difficulty)
  }
  
  if (category) {
    filtered = filtered.filter(tutorial => tutorial.category === category)
  }
  
  if (type) {
    filtered = filtered.filter(tutorial => tutorial.type === type)
  }
  
  res.json(filtered)
})

// 学习路径
app.get('/api/intelligence/learning-paths', (req, res) => {
  res.json([
    {
      id: '1',
      title: 'Apple Silicon 容器开发',
      description: '从零开始学习在 Apple Silicon 上进行容器化开发',
      icon: 'apple',
      level: '初级',
      duration: '6周',
      students: 1247,
      rating: 4.8,
      progress: 35,
      modules: ['Docker 基础', 'Apple Silicon 优化', '实战项目']
    }
  ])
})

// 社区讨论
app.get('/api/intelligence/discussions', (req, res) => {
  res.json([
    {
      id: '1',
      title: 'Apple Silicon 上的 Docker 性能优化技巧分享',
      content: '经过几个月的实践，总结了一些在 Apple Silicon 芯片上优化 Docker 性能的实用技巧...',
      tags: ['Docker', 'Apple Silicon', '性能优化'],
      author: {
        name: 'TechGuru',
        avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=40&h=40&fit=crop&crop=face',
        badge: 'star'
      },
      createdAt: '2小时前',
      replies: 23,
      likes: 156,
      views: 2340
    }
  ])
})

// 工具
app.get('/api/intelligence/tools', (req, res) => {
  const { category, sort } = req.query
  let filtered = [...mockTools]
  
  if (category && category !== 'all') {
    filtered = filtered.filter(tool => tool.category === category)
  }
  
  // 排序逻辑
  switch (sort) {
    case 'rating':
      filtered.sort((a, b) => b.rating - a.rating)
      break
    case 'name':
      filtered.sort((a, b) => a.name.localeCompare(b.name))
      break
    case 'popular':
    default:
      filtered.sort((a, b) => b.users - a.users)
      break
  }
  
  res.json(filtered)
})

// 搜索
app.get('/api/intelligence/search', (req, res) => {
  const { q, type } = req.query
  const results = []
  
  if (!q) {
    return res.json(results)
  }
  
  const searchLower = q.toLowerCase()
  
  // 搜索镜像
  if (!type || type === 'images') {
    const imageResults = mockImageRepositories.filter(repo =>
      repo.name.toLowerCase().includes(searchLower) ||
      repo.description.toLowerCase().includes(searchLower)
    ).map(repo => ({ ...repo, type: 'image' }))
    results.push(...imageResults)
  }
  
  // 搜索新闻
  if (!type || type === 'news') {
    const newsResults = mockNewsArticles.filter(article =>
      article.title.toLowerCase().includes(searchLower) ||
      article.summary.toLowerCase().includes(searchLower)
    ).map(article => ({ ...article, type: 'news' }))
    results.push(...newsResults)
  }
  
  res.json(results)
})

// 健康检查
app.get('/api/health', (req, res) => {
  res.json({ status: 'ok', timestamp: new Date().toISOString() })
})

// 启动服务器
app.listen(port, () => {
  console.log(`Intelligence Center API server running at http://localhost:${port}`)
  console.log('Available endpoints:')
  console.log('  GET /api/intelligence/images')
  console.log('  GET /api/intelligence/categories')
  console.log('  GET /api/intelligence/news')
  console.log('  GET /api/intelligence/tutorials')
  console.log('  GET /api/intelligence/learning-paths')
  console.log('  GET /api/intelligence/discussions')
  console.log('  GET /api/intelligence/tools')
  console.log('  GET /api/intelligence/search')
  console.log('  GET /api/health')
})

module.exports = app
