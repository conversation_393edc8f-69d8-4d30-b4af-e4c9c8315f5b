const express = require('express');
const cors = require('cors');
const { exec } = require('child_process');
const { promisify } = require('util');
const WebSocket = require('ws');
const http = require('http');

const execAsync = promisify(exec);
const app = express();
const server = http.createServer(app);
const wss = new WebSocket.Server({ server });

// 中间件
app.use(cors());
app.use(express.json());

// 执行苹果容器命令的通用函数
async function executeContainerCommand(command) {
  try {
    const { stdout, stderr } = await execAsync(`container ${command}`);
    if (stderr && !stderr.includes('Warning')) {
      throw new Error(stderr);
    }
    return stdout.trim();
  } catch (error) {
    console.error(`执行容器命令失败: container ${command}`, error);
    throw error;
  }
}

// 解析容器列表输出
function parseContainerList(output) {
  const lines = output.split('\n').filter(line => line.trim());
  const containers = [];
  
  // 跳过标题行
  for (let i = 1; i < lines.length; i++) {
    const line = lines[i].trim();
    if (!line) continue;
    
    const parts = line.split(/\s+/);
    if (parts.length >= 6) {
      containers.push({
        id: parts[0],
        name: parts[0],
        image: parts[1],
        os: parts[2],
        arch: parts[3],
        state: parts[4],
        addr: parts[5]
      });
    }
  }
  
  return containers;
}

// 解析镜像列表输出
function parseImageList(output) {
  const lines = output.split('\n').filter(line => line.trim());
  const images = [];
  
  // 跳过标题行
  for (let i = 1; i < lines.length; i++) {
    const line = lines[i].trim();
    if (!line) continue;
    
    const parts = line.split(/\s+/);
    if (parts.length >= 3) {
      const [name, tag] = parts[0].includes(':') ? parts[0].split(':') : [parts[0], parts[1]];
      images.push({
        id: `${parts[0]}-${parts[1]}`,
        name: name,
        tag: tag || parts[1],
        digest: parts[2],
        architecture: 'arm64'
      });
    }
  }
  
  return images;
}

// API 路由

// 获取容器列表
app.get('/api/containers', async (req, res) => {
  try {
    const output = await executeContainerCommand('list');
    const containers = parseContainerList(output);
    res.json(containers);
  } catch (error) {
    console.error('获取容器列表失败:', error);
    res.status(500).json({ error: '获取容器列表失败' });
  }
});

// 获取镜像列表
app.get('/api/images', async (req, res) => {
  try {
    const output = await executeContainerCommand('images list');
    const images = parseImageList(output);
    res.json(images);
  } catch (error) {
    console.error('获取镜像列表失败:', error);
    res.status(500).json({ error: '获取镜像列表失败' });
  }
});

// 获取系统信息
app.get('/api/system', async (req, res) => {
  try {
    const [containersOutput, imagesOutput, statusOutput] = await Promise.all([
      executeContainerCommand('list'),
      executeContainerCommand('images list'),
      executeContainerCommand('system status')
    ]);
    
    const containers = parseContainerList(containersOutput);
    const images = parseImageList(imagesOutput);
    const runningContainers = containers.filter(c => c.state === 'running').length;
    const apiServerStatus = statusOutput.includes('apiserver is running') ? 'running' : 'stopped';
    
    const systemInfo = {
      containerCount: containers.length,
      runningContainers,
      imageCount: images.length,
      version: 'Apple Container 2025.6',
      apiServerStatus,
      architecture: 'Apple M4',
      platform: 'macOS Tahoe 26.0'
    };
    
    res.json(systemInfo);
  } catch (error) {
    console.error('获取系统信息失败:', error);
    res.status(500).json({ error: '获取系统信息失败' });
  }
});

// 启动容器
app.post('/api/containers/:id/start', async (req, res) => {
  try {
    await executeContainerCommand(`start ${req.params.id}`);
    res.json({ success: true, message: '容器启动成功' });
  } catch (error) {
    console.error(`启动容器失败: ${req.params.id}`, error);
    res.status(500).json({ error: '启动容器失败' });
  }
});

// 停止容器
app.post('/api/containers/:id/stop', async (req, res) => {
  try {
    await executeContainerCommand(`stop ${req.params.id}`);
    res.json({ success: true, message: '容器停止成功' });
  } catch (error) {
    console.error(`停止容器失败: ${req.params.id}`, error);
    res.status(500).json({ error: '停止容器失败' });
  }
});

// 重启容器
app.post('/api/containers/:id/restart', async (req, res) => {
  try {
    await executeContainerCommand(`restart ${req.params.id}`);
    res.json({ success: true, message: '容器重启成功' });
  } catch (error) {
    console.error(`重启容器失败: ${req.params.id}`, error);
    res.status(500).json({ error: '重启容器失败' });
  }
});

// 获取容器日志
app.get('/api/containers/:id/logs', async (req, res) => {
  try {
    const lines = req.query.lines || 100;
    const logs = await executeContainerCommand(`logs ${req.params.id} --tail ${lines}`);
    res.json({ logs });
  } catch (error) {
    console.error(`获取容器日志失败: ${req.params.id}`, error);
    res.status(500).json({ error: '获取容器日志失败' });
  }
});

// 删除镜像
app.delete('/api/images/:id', async (req, res) => {
  try {
    await executeContainerCommand(`rmi ${req.params.id}`);
    res.json({ success: true, message: '镜像删除成功' });
  } catch (error) {
    console.error(`删除镜像失败: ${req.params.id}`, error);
    res.status(500).json({ error: '删除镜像失败' });
  }
});

// WebSocket 连接处理
wss.on('connection', (ws) => {
  console.log('WebSocket 客户端已连接');
  
  // 发送实时系统状态
  const sendSystemStatus = async () => {
    try {
      const response = await fetch('http://localhost:3001/api/system');
      const systemInfo = await response.json();
      
      ws.send(JSON.stringify({
        type: 'system_status',
        data: systemInfo
      }));
    } catch (error) {
      console.error('发送系统状态失败:', error);
    }
  };
  
  // 每5秒发送一次系统状态
  const statusInterval = setInterval(sendSystemStatus, 5000);
  
  // 立即发送一次
  sendSystemStatus();
  
  ws.on('close', () => {
    console.log('WebSocket 客户端已断开');
    clearInterval(statusInterval);
  });
  
  ws.on('error', (error) => {
    console.error('WebSocket 错误:', error);
    clearInterval(statusInterval);
  });
});

// 健康检查端点
app.get('/health', (req, res) => {
  res.json({ 
    status: 'healthy', 
    timestamp: new Date().toISOString(),
    version: '1.0.0'
  });
});

// 启动服务器
const PORT = process.env.PORT || 3001;
server.listen(PORT, () => {
  console.log(`🚀 Apple Container API 服务器运行在 http://localhost:${PORT}`);
  console.log(`📡 WebSocket 服务器运行在 ws://localhost:${PORT}`);
});

// 优雅关闭
process.on('SIGTERM', () => {
  console.log('收到 SIGTERM 信号，正在关闭服务器...');
  server.close(() => {
    console.log('服务器已关闭');
    process.exit(0);
  });
});
