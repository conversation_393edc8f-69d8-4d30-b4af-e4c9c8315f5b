@custom-variant dark (&:is(.dark *));

:root {
  --font-size: 14px;

  /* Dark Mode Color Palette (from screenshot) */
  --dark-bg: #0f172a;
  --dark-card: #1e293b;
  --dark-primary-text: #ffffff;
  --dark-secondary-text: #94a3b8;
  --dark-positive: #22c55e;
  --dark-negative: #ef4444;
  --dark-cta: #3b82f6;
  --dark-tag: #334155;
  --dark-hover: #1e2535;
  --dark-table-hover: #1c2a3a;
  --dark-border: #374151;

  /* Updated theme variables for dark mode */
  --background: #0f172a;
  --foreground: #ffffff;
  --card: #1e293b;
  --card-foreground: #ffffff;
  --popover: #1e293b;
  --popover-foreground: #ffffff;
  --primary: #3b82f6;
  --primary-foreground: #ffffff;
  --secondary: #334155;
  --secondary-foreground: #ffffff;
  --muted: #1e293b;
  --muted-foreground: #94a3b8;
  --accent: #3b82f6;
  --accent-foreground: #ffffff;
  --destructive: #ef4444;
  --destructive-foreground: #ffffff;
  --border: #374151;
  --input: #1e293b;
  --input-background: #1e293b;
  --switch-background: #374151;
  --font-weight-medium: 600;
  --font-weight-normal: 400;
  --ring: #3b82f6;

  /* Chart colors using dark theme palette */
  --chart-1: #3b82f6;
  --chart-2: #22c55e;
  --chart-3: #94a3b8;
  --chart-4: #f59e0b;
  --chart-5: #ef4444;

  --radius: 0.75rem;

  /* Sidebar colors for dark theme */
  --sidebar: #0f172a;
  --sidebar-foreground: #ffffff;
  --sidebar-primary: #ffffff;
  --sidebar-primary-foreground: #0f172a;
  --sidebar-accent: #3b82f6;
  --sidebar-accent-foreground: #ffffff;
  --sidebar-border: #374151;
  --sidebar-ring: #3b82f6;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-destructive-foreground: var(--destructive-foreground);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-input-background: var(--input-background);
  --color-switch-background: var(--switch-background);
  --color-ring: var(--ring);
  --color-chart-1: var(--chart-1);
  --color-chart-2: var(--chart-2);
  --color-chart-3: var(--chart-3);
  --color-chart-4: var(--chart-4);
  --color-chart-5: var(--chart-5);
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
  --color-sidebar: var(--sidebar);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-primary-foreground: var(
    --sidebar-primary-foreground
  );
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-accent-foreground: var(
    --sidebar-accent-foreground
  );
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-ring: var(--sidebar-ring);

  /* Dark theme specific colors */
  --color-dark-bg: var(--dark-bg);
  --color-dark-card: var(--dark-card);
  --color-dark-primary-text: var(--dark-primary-text);
  --color-dark-secondary-text: var(--dark-secondary-text);
  --color-dark-positive: var(--dark-positive);
  --color-dark-negative: var(--dark-negative);
  --color-dark-cta: var(--dark-cta);
  --color-dark-tag: var(--dark-tag);
  --color-dark-hover: var(--dark-hover);
  --color-dark-table-hover: var(--dark-table-hover);
  --color-dark-border: var(--dark-border);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }

  body {
    @apply bg-background text-foreground;
    font-family:
      "Inter",
      system-ui,
      -apple-system,
      sans-serif;
    font-weight: 400;
  }
}

/**
 * Base typography with dark theme styling
 */
@layer base {
  :where(
    :not(:has([class*=" text-"]), :not(:has([class^="text-"])))
  ) {
    h1 {
      font-size: 1.875rem;
      font-weight: 700;
      line-height: 1.4;
      color: var(--dark-primary-text);
    }

    h2 {
      font-size: 1.5rem;
      font-weight: 600;
      line-height: 1.4;
      color: var(--dark-primary-text);
    }

    h3 {
      font-size: 1.25rem;
      font-weight: 600;
      line-height: 1.4;
      color: var(--dark-primary-text);
    }

    h4 {
      font-size: 1rem;
      font-weight: 600;
      line-height: 1.4;
      color: var(--dark-primary-text);
    }

    p {
      font-size: 0.875rem;
      font-weight: 400;
      line-height: 1.5;
      color: var(--dark-secondary-text);
    }

    label {
      font-size: 0.875rem;
      font-weight: 500;
      line-height: 1.4;
      color: var(--dark-primary-text);
    }

    button {
      font-size: 0.875rem;
      font-weight: 600;
      line-height: 1.4;
    }

    input {
      font-size: 0.875rem;
      font-weight: 400;
      line-height: 1.4;
    }
  }
}

html {
  font-size: var(--font-size);
}

/* Dark theme utility classes */
@layer utilities {
  /* Text colors */
  .text-dark-primary {
    color: var(--color-dark-primary-text);
  }
  .text-dark-secondary {
    color: var(--color-dark-secondary-text);
  }
  .text-dark-positive {
    color: var(--color-dark-positive);
  }
  .text-dark-negative {
    color: var(--color-dark-negative);
  }
  .text-dark-cta {
    color: var(--color-dark-cta);
  }

  /* Background colors */
  .bg-dark-bg {
    background-color: var(--color-dark-bg);
  }
  .bg-dark-card {
    background-color: var(--color-dark-card);
  }
  .bg-dark-tag {
    background-color: var(--color-dark-tag);
  }
  .bg-dark-hover {
    background-color: var(--color-dark-hover);
  }
  .bg-dark-table-hover {
    background-color: var(--color-dark-table-hover);
  }

  /* Border colors */
  .border-dark-color {
    border-color: var(--color-dark-border);
  }
}

/* Enhanced component styling for dark theme */
@layer components {
  .dark-card {
    border-radius: 0.75rem;
    border: 1px solid var(--color-dark-border);
    padding: 1.5rem;
    background-color: var(--color-dark-card);
    box-shadow: rgba(0, 0, 0, 0.25) 0px 4px 12px;
  }

  .dark-button-primary {
    font-weight: 600;
    padding: 0.75rem 1.5rem;
    border-radius: 0.75rem;
    transition: all 0.2s ease;
    color: #ffffff;
    background-color: var(--color-dark-cta);
  }

  .dark-button-primary:hover {
    background-color: #2563eb;
    box-shadow:
      0 0 0 1px var(--color-dark-cta),
      0 0 8px rgba(59, 130, 246, 0.4);
  }

  .dark-button-secondary {
    font-weight: 600;
    padding: 0.75rem 1.5rem;
    border-radius: 0.75rem;
    transition: all 0.2s ease;
    background-color: transparent;
    color: var(--color-dark-cta);
    border: 1px solid var(--color-dark-cta);
  }

  .dark-button-secondary:hover {
    background-color: rgba(59, 130, 246, 0.1);
  }

  .dark-nav-item {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.75rem 1rem;
    border-radius: 0.75rem;
    font-size: 0.875rem;
    font-weight: 500;
    transition: all 0.2s ease;
    color: var(--color-dark-secondary-text);
  }

  .dark-nav-item-active {
    background-color: var(--color-dark-hover);
    color: var(--color-dark-primary-text);
    border: 1px solid var(--color-dark-cta);
  }

  .dark-nav-item:hover {
    background-color: var(--color-dark-hover);
    color: var(--color-dark-primary-text);
  }

  .dark-shadow {
    box-shadow: rgba(0, 0, 0, 0.25) 0px 4px 12px;
  }

  .dark-shadow-lg {
    box-shadow: rgba(0, 0, 0, 0.4) 0px 8px 24px;
  }

  .dark-tag {
    padding: 0.25rem 0.75rem;
    border-radius: 9999px;
    font-size: 0.875rem;
    font-weight: 500;
    color: #ffffff;
    background-color: var(--color-dark-tag);
  }

  .dark-metric-positive {
    color: var(--color-dark-positive);
  }

  .dark-metric-negative {
    color: var(--color-dark-negative);
  }
}